package cmd

import (
	"context"
	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
	"halalplus/app/notify-svc/internal/controller/message"
	"halalplus/app/notify-svc/internal/controller/otp"
	"halalplus/utility"
	"halalplus/utility/unary"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcmd"
)

var (
	Main = gcmd.Command{
		Name:  "notify-svc",
		Usage: "notify-svc",
		Brief: "start notify-svc grpc server",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
			c := grpcx.Server.NewConfig()
			c.Name = "notify-svc"
			if g.<PERSON>y(c.Address) {
				c.Address = utility.GetLocalLANIP() + ":0"
			}
			c.Options = append(c.Options, []grpc.ServerOption{
				grpcx.Server.ChainUnary(
					unary.UnaryCommonError,
					grpcx.Server.UnaryValidate,
				)}...,
			)
			s := grpcx.Server.New(c)
			otp.Register(s)
			message.Register(s)
			// NOTICE: 一行代码，注册反射服务
			reflection.Register(s.Server)

			go runWebsocketServer()

			s.Run()
			return nil
		},
	}
)
