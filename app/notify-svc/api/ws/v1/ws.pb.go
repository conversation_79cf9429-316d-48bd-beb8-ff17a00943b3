// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: ws/v1/ws.proto

package v1

import (
	common "halalplus/api/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// MessageEnvelope 是所有 WebSocket 通信的顶级消息信封
// 通过将消息类型分为 C2S (Client to Server) 和 S2C (Server to Client)，用于区分消息是由谁发出的
type MessageEnvelope struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Payload:
	//
	//	*MessageEnvelope_C2SAuthMessage
	//	*MessageEnvelope_S2CSystemNotification
	Payload       isMessageEnvelope_Payload `protobuf_oneof:"payload" dc:"*MessageEnvelope_C2SAuthMessage*MessageEnvelope_S2CSystemNotification"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageEnvelope) Reset() {
	*x = MessageEnvelope{}
	mi := &file_ws_v1_ws_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageEnvelope) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageEnvelope) ProtoMessage() {}

func (x *MessageEnvelope) ProtoReflect() protoreflect.Message {
	mi := &file_ws_v1_ws_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageEnvelope.ProtoReflect.Descriptor instead.
func (*MessageEnvelope) Descriptor() ([]byte, []int) {
	return file_ws_v1_ws_proto_rawDescGZIP(), []int{0}
}

func (x *MessageEnvelope) GetPayload() isMessageEnvelope_Payload {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *MessageEnvelope) GetC2SAuthMessage() *C2SAuthMessage {
	if x != nil {
		if x, ok := x.Payload.(*MessageEnvelope_C2SAuthMessage); ok {
			return x.C2SAuthMessage
		}
	}
	return nil
}

func (x *MessageEnvelope) GetS2CSystemNotification() *S2CSystemNotification {
	if x != nil {
		if x, ok := x.Payload.(*MessageEnvelope_S2CSystemNotification); ok {
			return x.S2CSystemNotification
		}
	}
	return nil
}

type isMessageEnvelope_Payload interface {
	isMessageEnvelope_Payload()
}

type MessageEnvelope_C2SAuthMessage struct {
	// 客户端 -> 服务器 消息
	// 将 C2S 消息类型从 1 开始
	// 所有认证相关的消息放在 1-10 之间
	C2SAuthMessage *C2SAuthMessage `protobuf:"bytes,1,opt,name=c2s_auth_message,json=c2sAuthMessage,proto3,oneof" dc:"客户端 -> 服务器 消息将 C2S 消息类型从 1 开始所有认证相关的消息放在 1-10 之间发送登录信息"` // 发送登录信息
}

type MessageEnvelope_S2CSystemNotification struct {
	// 服务器 -> 客户端 消息
	// S2C 消息类型从 1001 开始
	S2CSystemNotification *S2CSystemNotification `protobuf:"bytes,1000,opt,name=s2c_system_notification,json=s2cSystemNotification,proto3,oneof" dc:"服务器 -> 客户端 消息S2C 消息类型从 1001 开始服务器发送的系统通知"` // 服务器发送的系统通知
}

func (*MessageEnvelope_C2SAuthMessage) isMessageEnvelope_Payload() {}

func (*MessageEnvelope_S2CSystemNotification) isMessageEnvelope_Payload() {}

// 客户端发送给服务器的聊天消息
type C2SAuthMessage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty" dc:"登录返回的jwt token"`                     // 登录返回的jwt token
	UserId        uint64                 `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty" dc:"用户ID"`              // 用户ID
	FrontInfo     *common.FrontInfo      `protobuf:"bytes,3,opt,name=front_info,json=frontInfo,proto3" json:"front_info,omitempty" dc:"FrontInfo"` // FrontInfo
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2SAuthMessage) Reset() {
	*x = C2SAuthMessage{}
	mi := &file_ws_v1_ws_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SAuthMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SAuthMessage) ProtoMessage() {}

func (x *C2SAuthMessage) ProtoReflect() protoreflect.Message {
	mi := &file_ws_v1_ws_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SAuthMessage.ProtoReflect.Descriptor instead.
func (*C2SAuthMessage) Descriptor() ([]byte, []int) {
	return file_ws_v1_ws_proto_rawDescGZIP(), []int{1}
}

func (x *C2SAuthMessage) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *C2SAuthMessage) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *C2SAuthMessage) GetFrontInfo() *common.FrontInfo {
	if x != nil {
		return x.FrontInfo
	}
	return nil
}

// 服务器发送给客户端的系统通知
type S2CAck struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CAck) Reset() {
	*x = S2CAck{}
	mi := &file_ws_v1_ws_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CAck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CAck) ProtoMessage() {}

func (x *S2CAck) ProtoReflect() protoreflect.Message {
	mi := &file_ws_v1_ws_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CAck.ProtoReflect.Descriptor instead.
func (*S2CAck) Descriptor() ([]byte, []int) {
	return file_ws_v1_ws_proto_rawDescGZIP(), []int{2}
}

type S2CSystemNotification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Title         string                 `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Timestamp     uint64                 `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CSystemNotification) Reset() {
	*x = S2CSystemNotification{}
	mi := &file_ws_v1_ws_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CSystemNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CSystemNotification) ProtoMessage() {}

func (x *S2CSystemNotification) ProtoReflect() protoreflect.Message {
	mi := &file_ws_v1_ws_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CSystemNotification.ProtoReflect.Descriptor instead.
func (*S2CSystemNotification) Descriptor() ([]byte, []int) {
	return file_ws_v1_ws_proto_rawDescGZIP(), []int{3}
}

func (x *S2CSystemNotification) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *S2CSystemNotification) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *S2CSystemNotification) GetTimestamp() uint64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

var File_ws_v1_ws_proto protoreflect.FileDescriptor

const file_ws_v1_ws_proto_rawDesc = "" +
	"\n" +
	"\x0ews/v1/ws.proto\x12\x05ws.v1\x1a\x11common/base.proto\x1a\x17common/front_info.proto\"\xb8\x01\n" +
	"\x0fMessageEnvelope\x12A\n" +
	"\x10c2s_auth_message\x18\x01 \x01(\v2\x15.ws.v1.C2SAuthMessageH\x00R\x0ec2sAuthMessage\x12W\n" +
	"\x17s2c_system_notification\x18\xe8\a \x01(\v2\x1c.ws.v1.S2CSystemNotificationH\x00R\x15s2cSystemNotificationB\t\n" +
	"\apayload\"q\n" +
	"\x0eC2SAuthMessage\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\x04R\x06userId\x120\n" +
	"\n" +
	"front_info\x18\x03 \x01(\v2\x11.common.FrontInfoR\tfrontInfo\"\b\n" +
	"\x06S2CAck\"e\n" +
	"\x15S2CSystemNotification\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1c\n" +
	"\ttimestamp\x18\x03 \x01(\x04R\ttimestampB$Z\"halalplus/app/notify-svc/api/ws/v1b\x06proto3"

var (
	file_ws_v1_ws_proto_rawDescOnce sync.Once
	file_ws_v1_ws_proto_rawDescData []byte
)

func file_ws_v1_ws_proto_rawDescGZIP() []byte {
	file_ws_v1_ws_proto_rawDescOnce.Do(func() {
		file_ws_v1_ws_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_ws_v1_ws_proto_rawDesc), len(file_ws_v1_ws_proto_rawDesc)))
	})
	return file_ws_v1_ws_proto_rawDescData
}

var file_ws_v1_ws_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_ws_v1_ws_proto_goTypes = []any{
	(*MessageEnvelope)(nil),       // 0: ws.v1.MessageEnvelope
	(*C2SAuthMessage)(nil),        // 1: ws.v1.C2SAuthMessage
	(*S2CAck)(nil),                // 2: ws.v1.S2CAck
	(*S2CSystemNotification)(nil), // 3: ws.v1.S2CSystemNotification
	(*common.FrontInfo)(nil),      // 4: common.FrontInfo
}
var file_ws_v1_ws_proto_depIdxs = []int32{
	1, // 0: ws.v1.MessageEnvelope.c2s_auth_message:type_name -> ws.v1.C2SAuthMessage
	3, // 1: ws.v1.MessageEnvelope.s2c_system_notification:type_name -> ws.v1.S2CSystemNotification
	4, // 2: ws.v1.C2SAuthMessage.front_info:type_name -> common.FrontInfo
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_ws_v1_ws_proto_init() }
func file_ws_v1_ws_proto_init() {
	if File_ws_v1_ws_proto != nil {
		return
	}
	file_ws_v1_ws_proto_msgTypes[0].OneofWrappers = []any{
		(*MessageEnvelope_C2SAuthMessage)(nil),
		(*MessageEnvelope_S2CSystemNotification)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_ws_v1_ws_proto_rawDesc), len(file_ws_v1_ws_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_ws_v1_ws_proto_goTypes,
		DependencyIndexes: file_ws_v1_ws_proto_depIdxs,
		MessageInfos:      file_ws_v1_ws_proto_msgTypes,
	}.Build()
	File_ws_v1_ws_proto = out.File
	file_ws_v1_ws_proto_goTypes = nil
	file_ws_v1_ws_proto_depIdxs = nil
}
