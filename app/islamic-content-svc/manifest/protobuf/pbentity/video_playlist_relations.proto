// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message VideoPlaylistRelations {
    uint32 Id         = 1; // 主键ID
    uint32 PlaylistId = 2; // 播放列表ID
    uint32 VideoId    = 3; // 视频ID
    string VideoName  = 4; // 视频名称
    uint32 SortOrder  = 5; // 排序权重，数字越小越靠前
    uint64 CreateTime = 6; // 创建时间(毫秒时间戳)
    uint64 UpdateTime = 7; // 更新时间(毫秒时间戳)
    int64  DeleteTime = 8; // 删除时间
}