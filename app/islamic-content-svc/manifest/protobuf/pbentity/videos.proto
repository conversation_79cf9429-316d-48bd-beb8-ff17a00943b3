// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message Videos {
    uint32 Id               = 1;  // 主键ID
    uint32 CategoryId       = 2;  // 分类ID
    string VideoUrl         = 3;  // 视频文件URL
    uint64 VideoSize        = 4;  // 视频文件大小(字节)
    uint32 VideoDuration    = 5;  // 视频时长(秒)
    string VideoFormat      = 6;  // 视频格式：mp4, mov等
    string VideoCoverUrl    = 7;  // 视频封面图片URL
    uint64 ViewCount        = 8;  // 播放次数
    uint64 ShareCount       = 9;  // 分享次数
    uint64 CollectCount     = 10; // 收藏次数
    string CreatorName      = 11; // 创建者姓名
    string Author           = 12; // 视频作者
    string AuthorLogo       = 13; // 作者头像URL
    uint32 AuthorAuthStatus = 14; // 作者认证状态：0-未认证，1-已认证
    uint32 PublishState     = 15; // 发布状态：0-待发布，1-已发布，2-已下线
    uint32 IsRecommended    = 16; // 是否推荐，0-否，1-是
    int32  IsDraft          = 17; // 是否草稿状态，1是，0否
    uint64 CreateTime       = 18; // 创建时间(毫秒时间戳)
    uint64 PublishTime      = 19; // 发布时间(毫秒时间戳)
    uint64 UpdateTime       = 20; // 更新时间(毫秒时间戳)
    int64  DeleteTime       = 21; // 删除时间
}