// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message VideoPlaylistLanguages {
    uint32 Id          = 1; // 主键ID
    uint32 PlaylistId  = 2; // 播放列表ID
    uint32 LanguageId  = 3; // 语言ID：0-中文，1-英文，2-印尼语
    string Name        = 4; // 播放列表名称
    string ShortTitle  = 5; // 播放列表短标题
    string Description = 6; // 播放列表描述
    uint64 CreateTime  = 7; // 创建时间(毫秒时间戳)
    uint64 UpdateTime  = 8; // 更新时间(毫秒时间戳)
    int64  DeleteTime  = 9; // 删除时间
}