// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: islamic/v1/banner.proto

package islamicv1

import (
	common "halalplus/api/common"
	_ "halalplus/app/islamic-content-svc/api/pbentity"
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/wrapperspb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Banner列表请求
type BannerListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LanguageId uint32 `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"` // 语言ID: 0-中文, 1-英文, 2-印尼语
	BannerType string `protobuf:"bytes,2,opt,name=banner_type,json=bannerType,proto3" json:"banner_type,omitempty" dc:"Banner类型: home，   ibadah"` // Banner类型: home，   ibadah
}

func (x *BannerListReq) Reset() {
	*x = BannerListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_banner_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BannerListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BannerListReq) ProtoMessage() {}

func (x *BannerListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_banner_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BannerListReq.ProtoReflect.Descriptor instead.
func (*BannerListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_banner_proto_rawDescGZIP(), []int{0}
}

func (x *BannerListReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *BannerListReq) GetBannerType() string {
	if x != nil {
		return x.BannerType
	}
	return ""
}

// Banner信息
type BannerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"Banner ID"`                              // Banner ID
	LanguageId  uint32 `protobuf:"varint,2,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言ID"` // 语言ID
	Title       string `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty" dc:"广告标题"`                              // 广告标题
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty" dc:"广告描述"`                  // 广告描述
	ImageUrl    string `protobuf:"bytes,5,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty" dc:"广告图片URL"`     // 广告图片URL
	LinkUrl     string `protobuf:"bytes,6,opt,name=link_url,json=linkUrl,proto3" json:"link_url,omitempty" dc:"跳转链接URL"`        // 跳转链接URL
	SortOrder   uint32 `protobuf:"varint,7,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty" dc:"排序权重"`    // 排序权重
}

func (x *BannerInfo) Reset() {
	*x = BannerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_banner_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BannerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BannerInfo) ProtoMessage() {}

func (x *BannerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_banner_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BannerInfo.ProtoReflect.Descriptor instead.
func (*BannerInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_banner_proto_rawDescGZIP(), []int{1}
}

func (x *BannerInfo) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BannerInfo) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *BannerInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *BannerInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *BannerInfo) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *BannerInfo) GetLinkUrl() string {
	if x != nil {
		return x.LinkUrl
	}
	return ""
}

func (x *BannerInfo) GetSortOrder() uint32 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

// Banner列表数据
type BannerListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*BannerInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *BannerListData) Reset() {
	*x = BannerListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_banner_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BannerListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BannerListData) ProtoMessage() {}

func (x *BannerListData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_banner_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BannerListData.ProtoReflect.Descriptor instead.
func (*BannerListData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_banner_proto_rawDescGZIP(), []int{2}
}

func (x *BannerListData) GetList() []*BannerInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// Banner列表响应
type BannerListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32           `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string          `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error   `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *BannerListData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *BannerListRes) Reset() {
	*x = BannerListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_banner_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BannerListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BannerListRes) ProtoMessage() {}

func (x *BannerListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_banner_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BannerListRes.ProtoReflect.Descriptor instead.
func (*BannerListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_banner_proto_rawDescGZIP(), []int{3}
}

func (x *BannerListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *BannerListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *BannerListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BannerListRes) GetData() *BannerListData {
	if x != nil {
		return x.Data
	}
	return nil
}

// Banner点击统计请求
type BannerClickReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BannerId uint32 `protobuf:"varint,1,opt,name=banner_id,json=bannerId,proto3" json:"banner_id,omitempty" dc:"Banner ID"` // Banner ID
	DeviceId string `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty" dc:"设备唯一标识"`     // 设备唯一标识
}

func (x *BannerClickReq) Reset() {
	*x = BannerClickReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_banner_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BannerClickReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BannerClickReq) ProtoMessage() {}

func (x *BannerClickReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_banner_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BannerClickReq.ProtoReflect.Descriptor instead.
func (*BannerClickReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_banner_proto_rawDescGZIP(), []int{4}
}

func (x *BannerClickReq) GetBannerId() uint32 {
	if x != nil {
		return x.BannerId
	}
	return 0
}

func (x *BannerClickReq) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

// Banner点击统计响应
type BannerClickRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *BannerClickRes) Reset() {
	*x = BannerClickRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_banner_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BannerClickRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BannerClickRes) ProtoMessage() {}

func (x *BannerClickRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_banner_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BannerClickRes.ProtoReflect.Descriptor instead.
func (*BannerClickRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_banner_proto_rawDescGZIP(), []int{5}
}

func (x *BannerClickRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *BannerClickRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *BannerClickRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_islamic_v1_banner_proto protoreflect.FileDescriptor

var file_islamic_v1_banner_proto_rawDesc = []byte{
	0x0a, 0x17, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x69, 0x73, 0x6c, 0x61, 0x6d,
	0x69, 0x63, 0x2e, 0x76, 0x31, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x61,
	0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x2f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x51, 0x0a, 0x0d, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x22, 0xcc, 0x01, 0x0a, 0x0a, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x69, 0x6e, 0x6b, 0x55,
	0x72, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x22, 0x3c, 0x0a, 0x0e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x2a, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22,
	0x8a, 0x01, 0x0a, 0x0d, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x2e, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x69, 0x73, 0x6c,
	0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x4c, 0x69,
	0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x4a, 0x0a, 0x0e,
	0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x1b,
	0x0a, 0x09, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x22, 0x5b, 0x0a, 0x0e, 0x42, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67,
	0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x32, 0x9a, 0x01, 0x0a, 0x0d, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x42, 0x0a, 0x0a, 0x42, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x19, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x1a, 0x19, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x45, 0x0a, 0x0b, 0x42,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x12, 0x1a, 0x2e, 0x69, 0x73, 0x6c,
	0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x43, 0x6c,
	0x69, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x52,
	0x65, 0x73, 0x42, 0x3c, 0x5a, 0x3a, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f,
	0x61, 0x70, 0x70, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2d, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x73, 0x6c, 0x61,
	0x6d, 0x69, 0x63, 0x2f, 0x76, 0x31, 0x3b, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x76, 0x31,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_islamic_v1_banner_proto_rawDescOnce sync.Once
	file_islamic_v1_banner_proto_rawDescData = file_islamic_v1_banner_proto_rawDesc
)

func file_islamic_v1_banner_proto_rawDescGZIP() []byte {
	file_islamic_v1_banner_proto_rawDescOnce.Do(func() {
		file_islamic_v1_banner_proto_rawDescData = protoimpl.X.CompressGZIP(file_islamic_v1_banner_proto_rawDescData)
	})
	return file_islamic_v1_banner_proto_rawDescData
}

var file_islamic_v1_banner_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_islamic_v1_banner_proto_goTypes = []interface{}{
	(*BannerListReq)(nil),  // 0: islamic.v1.BannerListReq
	(*BannerInfo)(nil),     // 1: islamic.v1.BannerInfo
	(*BannerListData)(nil), // 2: islamic.v1.BannerListData
	(*BannerListRes)(nil),  // 3: islamic.v1.BannerListRes
	(*BannerClickReq)(nil), // 4: islamic.v1.BannerClickReq
	(*BannerClickRes)(nil), // 5: islamic.v1.BannerClickRes
	(*common.Error)(nil),   // 6: common.Error
}
var file_islamic_v1_banner_proto_depIdxs = []int32{
	1, // 0: islamic.v1.BannerListData.list:type_name -> islamic.v1.BannerInfo
	6, // 1: islamic.v1.BannerListRes.error:type_name -> common.Error
	2, // 2: islamic.v1.BannerListRes.data:type_name -> islamic.v1.BannerListData
	6, // 3: islamic.v1.BannerClickRes.error:type_name -> common.Error
	0, // 4: islamic.v1.BannerService.BannerList:input_type -> islamic.v1.BannerListReq
	4, // 5: islamic.v1.BannerService.BannerClick:input_type -> islamic.v1.BannerClickReq
	3, // 6: islamic.v1.BannerService.BannerList:output_type -> islamic.v1.BannerListRes
	5, // 7: islamic.v1.BannerService.BannerClick:output_type -> islamic.v1.BannerClickRes
	6, // [6:8] is the sub-list for method output_type
	4, // [4:6] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_islamic_v1_banner_proto_init() }
func file_islamic_v1_banner_proto_init() {
	if File_islamic_v1_banner_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_islamic_v1_banner_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BannerListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_banner_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BannerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_banner_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BannerListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_banner_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BannerListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_banner_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BannerClickReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_banner_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BannerClickRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_islamic_v1_banner_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_banner_proto_goTypes,
		DependencyIndexes: file_islamic_v1_banner_proto_depIdxs,
		MessageInfos:      file_islamic_v1_banner_proto_msgTypes,
	}.Build()
	File_islamic_v1_banner_proto = out.File
	file_islamic_v1_banner_proto_rawDesc = nil
	file_islamic_v1_banner_proto_goTypes = nil
	file_islamic_v1_banner_proto_depIdxs = nil
}
