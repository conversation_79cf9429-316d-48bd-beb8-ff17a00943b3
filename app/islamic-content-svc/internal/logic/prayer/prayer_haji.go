package prayer

import (
	"context"
	"fmt"
	"halalplus/app/islamic-content-svc/internal/dao"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/model/entity"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// GetHajiJadwalList 获取朝觐日程列表
func (s *sPrayer) GetHajiJadwalList(ctx context.Context, languageId uint) (*model.HajiJadwalListOutput, error) {
	var jadwalList []*model.HajiJadwalInfo
	err := dao.HajiJadwal.Ctx(ctx).As("hj").
		Fields("hj.id, hj.item_no, hjc.time_info, hjc.event_summary, '' as additional_info, '' as article_text").
		LeftJoin(dao.HajiJadwalContent.Table()+" hjc", "hjc.jadwal_id = hj.id").
		Where("hjc.language_id", languageId).
		Order("hj.item_no ASC").
		Scan(&jadwalList)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐日程列表失败:", err)
		return nil, err
	}

	// 查询年份描述信息
	// 按照当前的伊斯兰历来查吧
	var descResult struct {
		Description string `json:"description"`
		Year        int    `json:"year"`
	}

	err = dao.HajiJadwalDescription.Ctx(ctx).
		Fields("description, year").
		Where(dao.HajiJadwalDescription.Columns().LanguageId, languageId).
		OrderDesc(dao.HajiJadwalDescription.Columns().Year).
		Limit(1).
		Scan(&descResult)

	var description string
	var year string
	if err != nil {
		g.Log().Warning(ctx, "查询朝觐日程描述失败:", err)
		// 不影响主要功能，使用默认值
		description = ""
		year = "2025"
	} else {
		description = descResult.Description
		year = fmt.Sprintf("%d", descResult.Year)
	}

	return &model.HajiJadwalListOutput{
		List:        jadwalList,
		Description: description,
		Year:        year,
	}, nil
}

// GetHajiJadwalDetail 获取朝觐日程详情
func (s *sPrayer) GetHajiJadwalDetail(ctx context.Context, jadwalId uint64, languageId uint) (*model.HajiJadwalDetailOutput, error) {
	// 查询朝觐日程详情，联合查询多语言内容
	var jadwal model.HajiJadwalInfo
	err := dao.HajiJadwal.Ctx(ctx).As("hj").
		Fields("hj.id, hj.item_no, hjc.time_info, hjc.event_summary, hjc.additional_info, hjc.article_text").
		LeftJoin(dao.HajiJadwalContent.Table()+" hjc", "hjc.jadwal_id = hj.id").
		Where("hj.id", jadwalId).
		Where("hjc.language_id", languageId).
		Scan(&jadwal)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐日程详情失败:", err)
		return nil, err
	}

	if jadwal.Id == 0 {
		return nil, gerror.New("not found")
	}

	return &model.HajiJadwalDetailOutput{
		Jadwal: &jadwal,
	}, nil
}

// GetHajiUrutanList 获取朝觐仪式顺序列表
func (s *sPrayer) GetHajiUrutanList(ctx context.Context, languageId uint) (*model.HajiUrutanListOutput, error) {
	var urutanList []*model.HajiUrutanInfo
	err := dao.HajiUrutan.Ctx(ctx).As("hu").
		Fields("hu.id, hu.urutan_no, huc.urutan_name, huc.urutan_time, hu.icon_url, '' as urutan_content").
		LeftJoin(dao.HajiUrutanContent.Table()+" huc", "huc.urutan_id = hu.id").
		Where("huc.language_id", languageId).
		Order("hu.urutan_no ASC").
		Scan(&urutanList)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐仪式顺序列表失败:", err)
		return nil, err
	}

	return &model.HajiUrutanListOutput{
		List: urutanList,
	}, nil
}

// GetHajiUrutanDetail 获取朝觐仪式顺序详情
func (s *sPrayer) GetHajiUrutanDetail(ctx context.Context, urutanId uint64, languageId uint) (*model.HajiUrutanDetailOutput, error) {
	var urutan model.HajiUrutanInfo
	err := dao.HajiUrutan.Ctx(ctx).As("hu").
		Fields("hu.id, hu.urutan_no, huc.urutan_name, huc.urutan_time, hu.icon_url, huc.urutan_content").
		LeftJoin(dao.HajiUrutanContent.Table()+" huc", "huc.urutan_id = hu.id").
		Where("hu.id", urutanId).
		Where("huc.language_id", languageId).
		Scan(&urutan)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐仪式顺序详情失败:", err)
		return nil, err
	}

	if urutan.Id == 0 {
		return nil, gerror.New("not found")
	}

	return &model.HajiUrutanDetailOutput{
		Urutan: &urutan,
	}, nil
}

// GetHajiDoaRingkasList 获取朝觐祈祷文简要列表
func (s *sPrayer) GetHajiDoaRingkasList(ctx context.Context) ([]*model.HajiDoaRingkasInfo, error) {
	// 首先查询分类ID
	var category *entity.DoaCategories
	err := dao.DoaCategories.Ctx(ctx).
		Where("group_name", "doa").
		Where("slug", "doa-haji-umrah").
		Scan(&category)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐祈祷文分类失败:", err)
		return nil, err
	}
	if category == nil {
		g.Log().Warning(ctx, "未找到朝觐祈祷文分类")
		return []*model.HajiDoaRingkasInfo{}, nil
	}

	// 查询子分类列表，按order_number排序
	var subCategories []*entity.DoaSubCategories
	err = dao.DoaSubCategories.Ctx(ctx).
		Fields("id, name, order_number").
		Where("parent_id = ?", category.Id).
		Order("order_number ASC").
		Scan(&subCategories)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐祈祷文子分类失败:", err)
		return nil, err
	}

	if len(subCategories) == 0 {
		return []*model.HajiDoaRingkasInfo{}, nil
	}

	// 列表
	var doaList []*model.HajiDoaRingkasInfo
	subCategoryIds := make([]int, len(subCategories))
	doaMap := make(map[int]*model.HajiDoaRingkasInfo)

	for i, subCat := range subCategories {
		subCategoryIds[i] = subCat.Id
		doaInfo := &model.HajiDoaRingkasInfo{
			Id:       uint64(subCat.Id),
			DoaNo:    subCat.OrderNumber,
			DoaName:  subCat.Name,
			Contents: []*model.HajiDoaRingkasContentInfo{},
		}
		doaList = append(doaList, doaInfo)
		doaMap[subCat.Id] = doaInfo
	}

	// 批量查询祈祷文数据
	var doaDataList []*entity.DoaData
	err = dao.DoaData.Ctx(ctx).
		Fields("id, category_id, arabic, translate, transliteration, order_number").
		WhereIn("category_id", subCategoryIds).
		Order("category_id ASC, order_number ASC").
		Scan(&doaDataList)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐祈祷文数据失败:", err)
		return nil, err
	}

	// 组装
	for _, doaData := range doaDataList {
		if doa, exists := doaMap[doaData.CategoryId]; exists {
			contentInfo := &model.HajiDoaRingkasContentInfo{
				Id:           uint64(doaData.Id),
				ContentOrder: doaData.OrderNumber,
				Title:        "",
				MuqattaAt:    "",
			}

			// 根据translate字段判断内容映射
			if doaData.Translate == "" {
				// 如果translate为空，title就是arabic
				contentInfo.Title = doaData.Arabic
				contentInfo.ArabicText = ""
				contentInfo.IndonesianText = ""
				contentInfo.LatinText = ""
			} else {
				// 否则按照字段映射
				contentInfo.ArabicText = doaData.Arabic
				contentInfo.IndonesianText = doaData.Translate
				contentInfo.LatinText = doaData.Transliteration
			}

			doa.Contents = append(doa.Contents, contentInfo)
		}
	}

	return doaList, nil
}

// GetHajiDoaPanjangList 获取朝觐祈祷文详细列表
func (s *sPrayer) GetHajiDoaPanjangList(ctx context.Context) ([]*model.HajiDoaPanjangInfo, error) {
	var categories []*entity.DoaCategories
	err := dao.DoaCategories.Ctx(ctx).
		Fields("id, name, order_number, total").
		Where("group_name", "haji-dan-umrah").
		Order("id ASC").
		Scan(&categories)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐祈祷文详细列表失败:", err)
		return nil, err
	}

	var doaList []*model.HajiDoaPanjangInfo
	for _, category := range categories {
		doaInfo := &model.HajiDoaPanjangInfo{
			Id:          uint64(category.Id),
			DoaNo:       category.OrderNumber,
			DoaName:     category.Name,
			BacaanCount: 0, // 默认值，如果需要实际统计可以后续添加查询逻辑
		}
		doaList = append(doaList, doaInfo)
	}

	return doaList, nil
}

// GetHajiDoaPanjangBacaanList 获取朝觐祈祷文诵读内容列表
func (s *sPrayer) GetHajiDoaPanjangBacaanList(ctx context.Context, doaId uint64) (*model.HajiDoaPanjangBacaanListOutput, error) {
	// 先查询祈祷文基本信息
	var doaInfo struct {
		DoaNo   int    `json:"doa_no"`
		DoaName string `json:"doa_name"`
	}
	err := dao.HajiDoaPanjang.Ctx(ctx).
		Fields("doa_no, doa_name").
		Where("id", doaId).
		Scan(&doaInfo)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐祈祷文基本信息失败:", err)
		return nil, err
	}

	if doaInfo.DoaNo == 0 {
		return nil, gerror.New("not found")
	}

	// 查询诵读列表
	var bacaanList []*model.HajiDoaPanjangBacaanInfo
	err = dao.HajiDoaPanjangBacaan.Ctx(ctx).
		Fields("id, doa_id, bacaan_no, bacaan_name").
		Where("doa_id", doaId).
		Order("bacaan_no ASC").
		Scan(&bacaanList)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐祈祷文诵读列表失败:", err)
		return nil, err
	}

	if len(bacaanList) == 0 {
		return &model.HajiDoaPanjangBacaanListOutput{
			List:    bacaanList,
			DoaNo:   doaInfo.DoaNo,
			DoaName: doaInfo.DoaName,
		}, nil
	}

	// 提取所有bacaan_id
	bacaanIds := make([]uint64, len(bacaanList))
	bacaanMap := make(map[uint64]*model.HajiDoaPanjangBacaanInfo)
	for i, bacaan := range bacaanList {
		bacaanIds[i] = bacaan.Id
		bacaan.Contents = []*model.HajiDoaPanjangBacaanContentInfo{}
		bacaanMap[bacaan.Id] = bacaan
	}

	// 批量查询
	var contents []*entity.HajiDoaPanjangBacaanContent
	err = dao.HajiDoaPanjangBacaanContent.Ctx(ctx).
		Fields("id, bacaan_id, content_order, title, muqatta_at, arabic_text, indonesian_text, latin_text").
		WhereIn("bacaan_id", bacaanIds).
		Order("bacaan_id ASC, content_order ASC").
		Scan(&contents)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐祈祷文诵读内容失败:", err)
		return nil, err
	}

	for _, content := range contents {
		if bacaan, exists := bacaanMap[content.BacaanId]; exists {
			contentInfo := &model.HajiDoaPanjangBacaanContentInfo{
				Id:             content.Id,
				ContentOrder:   content.ContentOrder,
				Title:          content.Title,
				MuqattaAt:      content.MuqattaAt,
				ArabicText:     content.ArabicText,
				IndonesianText: content.IndonesianText,
				LatinText:      content.LatinText,
			}
			bacaan.Contents = append(bacaan.Contents, contentInfo)
		}
	}

	return &model.HajiDoaPanjangBacaanListOutput{
		List:    bacaanList,
		DoaNo:   doaInfo.DoaNo,
		DoaName: doaInfo.DoaName,
	}, nil
}

// GetHajiHikmahList 获取朝觐智慧列表
func (s *sPrayer) GetHajiHikmahList(ctx context.Context, languageId uint) (*model.HajiHikmahListOutput, error) {
	var hikmahList []*model.HajiHikmahInfo
	err := dao.HajiHikmah.Ctx(ctx).As("hh").
		Fields("hh.id, hh.article_id, hhl.title").
		LeftJoin(dao.HajiHikmahLanguages.Table()+" hhl", "hhl.hikmah_id = hh.id").
		Where("hhl.language_id", languageId).
		Order("hh.sort_order ASC, hh.id ASC").
		Scan(&hikmahList)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐智慧列表失败:", err)
		return nil, err
	}

	return &model.HajiHikmahListOutput{
		List: hikmahList,
	}, nil
}

// GetHajiNewsList 获取朝觐新闻列表
func (s *sPrayer) GetHajiNewsList(ctx context.Context, languageId uint, page, size int) (*model.HajiNewsListOutput, error) {
	headerTag := s.getHajiNewsHeaderTag(ctx, languageId)

	// 获取所有关联的 article_id
	articleIds, err := s.getHajiNewsArticleIds(ctx)
	if err != nil {
		return nil, err
	}

	if len(articleIds) == 0 {
		return &model.HajiNewsListOutput{
			List:      []*model.HajiNewsItem{},
			Total:     0,
			HeaderTag: headerTag,
		}, nil
	}

	// 先查询总数
	baseQuery := dao.NewsArticle.Ctx(ctx).As("na").
		LeftJoin(dao.NewsArticleLanguage.Table()+" nal", "nal.article_id = na.id").
		LeftJoin(dao.NewsCategoryLanguage.Table()+" ncl", "ncl.category_id = na.category_id").
		WhereIn("na.id", articleIds).
		Where("na.is_publish", 1).
		Where("nal.language_id", languageId).
		Where("ncl.language_id", languageId)

	total, err := baseQuery.Count()
	if err != nil {
		g.Log().Error(ctx, "查询朝觐新闻总数失败:", err)
		return nil, err
	}

	// 根据 article_id 查询文章详情，使用数据库分页
	type newsQueryResult struct {
		ArticleId    uint   `json:"article_id"`
		CategoryName string `json:"category_name"`
		Title        string `json:"title"`
		PublishTime  int64  `json:"publish_time"`
		CoverImage   string `json:"cover_image"`
	}

	var results []*newsQueryResult
	err = baseQuery.
		Fields("na.id as article_id, ncl.name as category_name, nal.name as title, na.publish_time, na.cover_imgs as cover_image").
		Order("na.publish_time DESC"). // 直接按发布时间降序排序
		Page(page, size).
		Scan(&results)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐新闻列表失败:", err)
		return nil, err
	}

	newsList := make([]*model.HajiNewsItem, 0, len(results))
	for _, result := range results {
		newsList = append(newsList, &model.HajiNewsItem{
			ArticleId:    uint64(result.ArticleId), // 类型转换：uint -> uint64
			CategoryName: result.CategoryName,
			Title:        result.Title,
			PublishTime:  result.PublishTime,
			CoverImage:   result.CoverImage,
		})
	}

	return &model.HajiNewsListOutput{
		List:      newsList,
		Total:     total,
		HeaderTag: headerTag,
	}, nil
}

// getHajiNewsArticleIds 获取朝觐新闻关联的所有文章ID
func (s *sPrayer) getHajiNewsArticleIds(ctx context.Context) ([]uint, error) {
	type articleIdResult struct {
		ArticleId uint `json:"article_id"`
	}
	var articleIdResults []articleIdResult
	err := dao.HajiNewsTagArticle.Ctx(ctx).
		Fields("DISTINCT article_id").
		Scan(&articleIdResults)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐新闻标签关联失败:", err)
		return nil, err
	}

	// 提取 article_id 到数组
	articleIds := make([]uint, len(articleIdResults))
	for i, result := range articleIdResults {
		articleIds[i] = result.ArticleId
	}
	return articleIds, nil
}

// getHajiNewsHeaderTag 获取朝觐新闻标签名（按sort_order最靠前的）
func (s *sPrayer) getHajiNewsHeaderTag(ctx context.Context, languageId uint) string {
	headerTagValue, err := dao.HajiNewsTag.Ctx(ctx).As("hnt").
		LeftJoin(dao.HajiNewsTagLanguages.Table()+" hntl", "hntl.tag_id = hnt.id").
		Where("hntl.language_id", languageId).
		Fields("hntl.tag_name").
		Order("hnt.sort_order ASC").
		Limit(1).
		Value()
	if err == nil && headerTagValue != nil {
		return headerTagValue.String()
	}
	if err != nil {
		g.Log().Warning(ctx, "查询朝觐新闻标签失败:", err)
	}
	// 不影响主要功能，使用默认值
	return ""
}

// GetHajiLandmarkList 获取朝觐地标列表（支持分页）
func (s *sPrayer) GetHajiLandmarkList(ctx context.Context, innerType string, languageId uint, page, size int) (*model.HajiLandmarkListOutput, error) {
	baseQuery := dao.HajiLandmark.Ctx(ctx).As("hl").
		LeftJoin(dao.HajiLandmarkLanguages.Table()+" hll", "hll.landmark_id = hl.id").
		LeftJoin(dao.HajiLandmarkType.Table()+" hlt", "hlt.id = hl.type_id").
		LeftJoin(dao.HajiLandmarkTypeLanguages.Table()+" hltl", "hltl.type_id = hl.type_id").
		Where("hll.language_id", languageId).
		Where("hltl.language_id", languageId)

	// 内部类型, destinasi(目的地), tokoh(人物)
	if innerType != "" {
		baseQuery = baseQuery.Where("hl.inner_type", innerType)
	}

	// 查询总数
	total, err := baseQuery.Count()
	if err != nil {
		g.Log().Error(ctx, "查询朝觐地标总数失败:", err)
		return nil, err
	}

	// 查询分页数据
	dataQuery := baseQuery.
		Fields("hl.id, hl.latitude, hl.longitude, hl.image_url, hll.landmark_name, hlt.icon_url, hltl.type_name").
		Order("hl.sort_order ASC, hl.id ASC").
		Page(page, size)

	type landmarkQueryResult struct {
		Id           uint64  `json:"id"`
		Latitude     float64 `json:"latitude"`
		Longitude    float64 `json:"longitude"`
		ImageUrl     string  `json:"image_url"`
		LandmarkName string  `json:"landmark_name"`
		IconUrl      string  `json:"icon_url"`
		TypeName     string  `json:"type_name"`
	}

	var results []*landmarkQueryResult
	err = dataQuery.Scan(&results)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐地标列表失败:", err)
		return nil, err
	}

	landmarkList := make([]*model.HajiLandmarkItem, 0, len(results))
	for _, result := range results {
		landmarkList = append(landmarkList, &model.HajiLandmarkItem{
			LandmarkId:   result.Id,
			Latitude:     result.Latitude,
			Longitude:    result.Longitude,
			ImageUrl:     result.ImageUrl,
			LandmarkName: result.LandmarkName,
			TypeIconUrl:  result.IconUrl,
			TypeName:     result.TypeName,
		})
	}

	return &model.HajiLandmarkListOutput{
		List:  landmarkList,
		Total: total,
	}, nil
}

// GetHajiLandmarkDetail 获取朝觐地标详情
func (s *sPrayer) GetHajiLandmarkDetail(ctx context.Context, landmarkId uint64, languageId uint) (*model.HajiLandmarkInfo, error) {
	type landmarkDetailResult struct {
		Id               uint64  `json:"id"`
		Latitude         float64 `json:"latitude"`
		Longitude        float64 `json:"longitude"`
		ImageUrl         string  `json:"image_url"`
		LandmarkName     string  `json:"landmark_name"`
		Country          string  `json:"country"`
		Address          string  `json:"address"`
		ShortDescription string  `json:"short_description"`
		InformationText  string  `json:"information_text"`
		IconUrl          string  `json:"icon_url"`
		TypeName         string  `json:"type_name"`
	}

	var result landmarkDetailResult
	err := dao.HajiLandmark.Ctx(ctx).As("hl").
		LeftJoin(dao.HajiLandmarkLanguages.Table()+" hll", "hll.landmark_id = hl.id").
		LeftJoin(dao.HajiLandmarkType.Table()+" hlt", "hlt.id = hl.type_id").
		LeftJoin(dao.HajiLandmarkTypeLanguages.Table()+" hltl", "hltl.type_id = hl.type_id").
		Where("hl.id", landmarkId).
		Where("hll.language_id", languageId).
		Where("hltl.language_id", languageId).
		Fields("hl.id, hl.latitude, hl.longitude, hl.image_url, hll.landmark_name, hll.country, hll.address, hll.short_description, hll.information_text, hlt.icon_url, hltl.type_name").
		Scan(&result)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐地标详情失败:", err)
		return nil, err
	}

	if result.Id == 0 {
		return nil, gerror.New("data not found")
	}

	return &model.HajiLandmarkInfo{
		LandmarkId:       result.Id,
		Latitude:         result.Latitude,
		Longitude:        result.Longitude,
		ImageUrl:         result.ImageUrl,
		LandmarkName:     result.LandmarkName,
		Country:          result.Country,
		Address:          result.Address,
		ShortDescription: result.ShortDescription,
		InformationText:  result.InformationText,
		TypeIconUrl:      result.IconUrl,
		TypeName:         result.TypeName,
	}, nil
}

// GetRamadhanDoaList 获取斋月祈祷文简要列表
func (s *sPrayer) GetRamadhanDoaList(ctx context.Context) ([]*model.RamadhanDoaInfo, error) {
	// 查询祈祷文基本信息
	var doaList []*model.RamadhanDoaInfo
	err := dao.RamadhanDoa.Ctx(ctx).
		Fields("id, doa_no, doa_name").
		Order("doa_no ASC").
		Scan(&doaList)
	if err != nil {
		g.Log().Error(ctx, "查询斋月祈祷文简要列表失败:", err)
		return nil, err
	}

	if len(doaList) == 0 {
		return doaList, nil
	}

	// 提取所有doa_id
	doaIds := make([]uint64, len(doaList))
	doaMap := make(map[uint64]*model.RamadhanDoaInfo)
	for i, doa := range doaList {
		doaIds[i] = doa.Id
		doa.Contents = []*model.RamadhanDoaContentInfo{}
		doaMap[doa.Id] = doa
	}

	// 批量查询
	var contents []*entity.RamadhanDoaContent
	err = dao.RamadhanDoaContent.Ctx(ctx).
		Fields("id, doa_id, content_order, title, muqatta_at, arabic_text, indonesian_text, latin_text").
		WhereIn("doa_id", doaIds).
		Order("doa_id ASC, content_order ASC").
		Scan(&contents)
	if err != nil {
		g.Log().Error(ctx, "查询斋月祈祷文内容失败:", err)
		return nil, err
	}

	for _, content := range contents {
		if doa, exists := doaMap[content.DoaId]; exists {
			contentInfo := &model.RamadhanDoaContentInfo{
				Id:             content.Id,
				ContentOrder:   content.ContentOrder,
				Title:          content.Title,
				MuqattaAt:      content.MuqattaAt,
				ArabicText:     content.ArabicText,
				IndonesianText: content.IndonesianText,
				LatinText:      content.LatinText,
			}
			doa.Contents = append(doa.Contents, contentInfo)
		}
	}

	return doaList, nil
}
