package prayer

import (
	"context"
	"halalplus/app/islamic-content-svc/internal/dao"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/model/entity"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// GetUmrahUrutanList 获取副朝仪式顺序列表
func (s *sPrayer) GetUmrahUrutanList(ctx context.Context, languageId uint) (*model.UmrahUrutanListOutput, error) {
	var urutanList []*model.UmrahUrutanInfo
	err := dao.UmrahUrutan.Ctx(ctx).As("uu").
		Fields("uu.id, uu.urutan_no, uuc.urutan_name, uuc.urutan_time, uu.icon_url, '' as urutan_content").
		LeftJoin(dao.UmrahUrutanContent.Table()+" uuc", "uuc.urutan_id = uu.id").
		Where("uuc.language_id", languageId).
		Order("uu.urutan_no ASC").
		Scan(&urutanList)
	if err != nil {
		g.Log().Error(ctx, "查询副朝仪式顺序列表失败:", err)
		return nil, err
	}

	return &model.UmrahUrutanListOutput{
		List: urutanList,
	}, nil
}

// GetUmrahUrutanDetail 获取副朝仪式顺序详情
func (s *sPrayer) GetUmrahUrutanDetail(ctx context.Context, urutanId uint64, languageId uint) (*model.UmrahUrutanDetailOutput, error) {
	var urutan model.UmrahUrutanInfo
	err := dao.UmrahUrutan.Ctx(ctx).As("uu").
		Fields("uu.id, uu.urutan_no, uuc.urutan_name, uuc.urutan_time, uu.icon_url, uuc.urutan_content").
		LeftJoin(dao.UmrahUrutanContent.Table()+" uuc", "uuc.urutan_id = uu.id").
		Where("uu.id", urutanId).
		Where("uuc.language_id", languageId).
		Scan(&urutan)
	if err != nil {
		g.Log().Error(ctx, "查询副朝仪式顺序详情失败:", err)
		return nil, err
	}

	if urutan.Id == 0 {
		return nil, gerror.New("not found")
	}

	return &model.UmrahUrutanDetailOutput{
		Urutan: &urutan,
	}, nil
}

// GetUmrahDoaRingkasList 获取副朝祈祷文简要列表
func (s *sPrayer) GetUmrahDoaRingkasList(ctx context.Context) ([]*model.UmrahDoaRingkasInfo, error) {
	// 查询祈祷文基本信息
	var doaList []*model.UmrahDoaRingkasInfo
	err := dao.UmrahDoaRingkas.Ctx(ctx).
		Fields("id, doa_no, doa_name").
		Order("doa_no ASC").
		Scan(&doaList)
	if err != nil {
		g.Log().Error(ctx, "查询副朝祈祷文简要列表失败:", err)
		return nil, err
	}

	if len(doaList) == 0 {
		return doaList, nil
	}

	// 提取所有doa_id
	doaIds := make([]uint64, len(doaList))
	doaMap := make(map[uint64]*model.UmrahDoaRingkasInfo)
	for i, doa := range doaList {
		doaIds[i] = doa.Id
		doa.Contents = []*model.UmrahDoaRingkasContentInfo{}
		doaMap[doa.Id] = doa
	}

	// 批量查询
	var contents []*entity.UmrahDoaRingkasContent
	err = dao.UmrahDoaRingkasContent.Ctx(ctx).
		Fields("id, doa_id, content_order, title, muqatta_at, arabic_text, indonesian_text, latin_text").
		WhereIn("doa_id", doaIds).
		Order("doa_id ASC, content_order ASC").
		Scan(&contents)
	if err != nil {
		g.Log().Error(ctx, "查询副朝祈祷文内容失败:", err)
		return nil, err
	}

	for _, content := range contents {
		if doa, exists := doaMap[content.DoaId]; exists {
			contentInfo := &model.UmrahDoaRingkasContentInfo{
				Id:             content.Id,
				ContentOrder:   content.ContentOrder,
				Title:          content.Title,
				MuqattaAt:      content.MuqattaAt,
				ArabicText:     content.ArabicText,
				IndonesianText: content.IndonesianText,
				LatinText:      content.LatinText,
			}
			doa.Contents = append(doa.Contents, contentInfo)
		}
	}

	return doaList, nil
}

// GetUmrahDoaPanjangList 获取副朝祈祷文详细列表
func (s *sPrayer) GetUmrahDoaPanjangList(ctx context.Context) ([]*model.UmrahDoaPanjangInfo, error) {
	var doaList []*model.UmrahDoaPanjangInfo
	err := dao.UmrahDoaPanjang.Ctx(ctx).
		Fields("id, doa_no, doa_name, bacaan_count").
		Order("doa_no ASC").
		Scan(&doaList)
	if err != nil {
		g.Log().Error(ctx, "查询副朝祈祷文详细列表失败:", err)
		return nil, err
	}

	return doaList, nil
}

// GetUmrahDoaPanjangBacaanList 获取副朝祈祷文诵读内容列表
func (s *sPrayer) GetUmrahDoaPanjangBacaanList(ctx context.Context, doaId uint64) (*model.UmrahDoaPanjangBacaanListOutput, error) {
	// 先查询祈祷文基本信息
	var doaInfo struct {
		DoaNo   int    `json:"doa_no"`
		DoaName string `json:"doa_name"`
	}
	err := dao.UmrahDoaPanjang.Ctx(ctx).
		Fields("doa_no, doa_name").
		Where("id", doaId).
		Scan(&doaInfo)
	if err != nil {
		g.Log().Error(ctx, "查询副朝祈祷文基本信息失败:", err)
		return nil, err
	}

	if doaInfo.DoaNo == 0 {
		return nil, gerror.New("not found")
	}

	// 查询诵读列表
	var bacaanList []*model.UmrahDoaPanjangBacaanInfo
	err = dao.UmrahDoaPanjangBacaan.Ctx(ctx).
		Fields("id, doa_id, bacaan_no, bacaan_name").
		Where("doa_id", doaId).
		Order("bacaan_no ASC").
		Scan(&bacaanList)
	if err != nil {
		g.Log().Error(ctx, "查询副朝祈祷文诵读列表失败:", err)
		return nil, err
	}

	if len(bacaanList) == 0 {
		return &model.UmrahDoaPanjangBacaanListOutput{
			List:    bacaanList,
			DoaNo:   doaInfo.DoaNo,
			DoaName: doaInfo.DoaName,
		}, nil
	}

	// 提取所有bacaan_id
	bacaanIds := make([]uint64, len(bacaanList))
	bacaanMap := make(map[uint64]*model.UmrahDoaPanjangBacaanInfo)
	for i, bacaan := range bacaanList {
		bacaanIds[i] = bacaan.Id
		bacaan.Contents = []*model.UmrahDoaPanjangBacaanContentInfo{}
		bacaanMap[bacaan.Id] = bacaan
	}

	// 批量查询
	var contents []*entity.UmrahDoaPanjangBacaanContent
	err = dao.UmrahDoaPanjangBacaanContent.Ctx(ctx).
		Fields("id, bacaan_id, content_order, title, muqatta_at, arabic_text, indonesian_text, latin_text").
		WhereIn("bacaan_id", bacaanIds).
		Order("bacaan_id ASC, content_order ASC").
		Scan(&contents)
	if err != nil {
		g.Log().Error(ctx, "查询副朝祈祷文诵读内容失败:", err)
		return nil, err
	}

	for _, content := range contents {
		if bacaan, exists := bacaanMap[content.BacaanId]; exists {
			contentInfo := &model.UmrahDoaPanjangBacaanContentInfo{
				Id:             content.Id,
				ContentOrder:   content.ContentOrder,
				Title:          content.Title,
				MuqattaAt:      content.MuqattaAt,
				ArabicText:     content.ArabicText,
				IndonesianText: content.IndonesianText,
				LatinText:      content.LatinText,
			}
			bacaan.Contents = append(bacaan.Contents, contentInfo)
		}
	}

	return &model.UmrahDoaPanjangBacaanListOutput{
		List:    bacaanList,
		DoaNo:   doaInfo.DoaNo,
		DoaName: doaInfo.DoaName,
	}, nil
}

// GetUmrahHikmahList 获取副朝智慧列表
func (s *sPrayer) GetUmrahHikmahList(ctx context.Context, languageId uint) (*model.UmrahHikmahListOutput, error) {
	var hikmahList []*model.UmrahHikmahInfo
	err := dao.UmrahHikmah.Ctx(ctx).As("uh").
		Fields("uh.id, uh.article_id, uhl.title").
		LeftJoin(dao.UmrahHikmahLanguages.Table()+" uhl", "uhl.hikmah_id = uh.id").
		Where("uhl.language_id", languageId).
		Order("uh.sort_order ASC, uh.id ASC").
		Scan(&hikmahList)
	if err != nil {
		g.Log().Error(ctx, "查询副朝智慧列表失败:", err)
		return nil, err
	}

	return &model.UmrahHikmahListOutput{
		List: hikmahList,
	}, nil
}

// GetUmrahLandmarkList 获取副朝地标列表（支持分页）
func (s *sPrayer) GetUmrahLandmarkList(ctx context.Context, innerType string, languageId uint, page, size int) (*model.UmrahLandmarkListOutput, error) {
	baseQuery := dao.UmrahLandmark.Ctx(ctx).As("ul").
		LeftJoin(dao.UmrahLandmarkLanguages.Table()+" ull", "ull.landmark_id = ul.id").
		LeftJoin(dao.UmrahLandmarkType.Table()+" ult", "ult.id = ul.type_id").
		LeftJoin(dao.UmrahLandmarkTypeLanguages.Table()+" ultl", "ultl.type_id = ul.type_id").
		Where("ull.language_id", languageId).
		Where("ultl.language_id", languageId)

	// 内部类型, destinasi(目的地), tokoh(人物)
	if innerType != "" {
		baseQuery = baseQuery.Where("ul.inner_type", innerType)
	}

	// 查询总数
	total, err := baseQuery.Count()
	if err != nil {
		g.Log().Error(ctx, "查询副朝地标总数失败:", err)
		return nil, err
	}

	// 查询分页数据
	dataQuery := baseQuery.
		Fields("ul.id, ul.latitude, ul.longitude, ul.image_url, ull.landmark_name, ult.icon_url, ultl.type_name").
		Order("ul.sort_order ASC, ul.id ASC").
		Page(page, size)

	type landmarkQueryResult struct {
		Id           uint64  `json:"id"`
		Latitude     float64 `json:"latitude"`
		Longitude    float64 `json:"longitude"`
		ImageUrl     string  `json:"image_url"`
		LandmarkName string  `json:"landmark_name"`
		IconUrl      string  `json:"icon_url"`
		TypeName     string  `json:"type_name"`
	}

	var results []*landmarkQueryResult
	err = dataQuery.Scan(&results)
	if err != nil {
		g.Log().Error(ctx, "查询副朝地标列表失败:", err)
		return nil, err
	}

	landmarkList := make([]*model.UmrahLandmarkItem, 0, len(results))
	for _, result := range results {
		landmarkList = append(landmarkList, &model.UmrahLandmarkItem{
			LandmarkId:   result.Id,
			Latitude:     result.Latitude,
			Longitude:    result.Longitude,
			ImageUrl:     result.ImageUrl,
			LandmarkName: result.LandmarkName,
			TypeIconUrl:  result.IconUrl,
			TypeName:     result.TypeName,
		})
	}

	return &model.UmrahLandmarkListOutput{
		List:  landmarkList,
		Total: total,
	}, nil
}

// GetUmrahLandmarkDetail 获取副朝地标详情
func (s *sPrayer) GetUmrahLandmarkDetail(ctx context.Context, landmarkId uint64, languageId uint) (*model.UmrahLandmarkInfo, error) {
	type landmarkDetailResult struct {
		Id               uint64  `json:"id"`
		Latitude         float64 `json:"latitude"`
		Longitude        float64 `json:"longitude"`
		ImageUrl         string  `json:"image_url"`
		LandmarkName     string  `json:"landmark_name"`
		Country          string  `json:"country"`
		Address          string  `json:"address"`
		ShortDescription string  `json:"short_description"`
		InformationText  string  `json:"information_text"`
		IconUrl          string  `json:"icon_url"`
		TypeName         string  `json:"type_name"`
	}

	var result landmarkDetailResult
	err := dao.UmrahLandmark.Ctx(ctx).As("ul").
		LeftJoin(dao.UmrahLandmarkLanguages.Table()+" ull", "ull.landmark_id = ul.id").
		LeftJoin(dao.UmrahLandmarkType.Table()+" ult", "ult.id = ul.type_id").
		LeftJoin(dao.UmrahLandmarkTypeLanguages.Table()+" ultl", "ultl.type_id = ul.type_id").
		Where("ul.id", landmarkId).
		Where("ull.language_id", languageId).
		Where("ultl.language_id", languageId).
		Fields("ul.id, ul.latitude, ul.longitude, ul.image_url, ull.landmark_name, ull.country, ull.address, ull.short_description, ull.information_text, ult.icon_url, ultl.type_name").
		Scan(&result)
	if err != nil {
		g.Log().Error(ctx, "查询副朝地标详情失败:", err)
		return nil, err
	}

	if result.Id == 0 {
		return nil, gerror.New("data not found")
	}

	return &model.UmrahLandmarkInfo{
		LandmarkId:       result.Id,
		Latitude:         result.Latitude,
		Longitude:        result.Longitude,
		ImageUrl:         result.ImageUrl,
		LandmarkName:     result.LandmarkName,
		Country:          result.Country,
		Address:          result.Address,
		ShortDescription: result.ShortDescription,
		InformationText:  result.InformationText,
		TypeIconUrl:      result.IconUrl,
		TypeName:         result.TypeName,
	}, nil
}
