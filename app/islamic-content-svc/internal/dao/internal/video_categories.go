// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// VideoCategoriesDao is the data access object for the table video_categories.
type VideoCategoriesDao struct {
	table    string                 // table is the underlying table name of the DAO.
	group    string                 // group is the database configuration group name of the current DAO.
	columns  VideoCategoriesColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler     // handlers for customized model modification.
}

// VideoCategoriesColumns defines and stores column names for the table video_categories.
type VideoCategoriesColumns struct {
	Id         string // 主键ID
	VideoCount string // 分类下视频数量
	Remark     string // 备注
	CreateTime string // 创建时间(毫秒时间戳)
	UpdateTime string // 更新时间(毫秒时间戳)
	DeleteTime string // 删除时间
}

// videoCategoriesColumns holds the columns for the table video_categories.
var videoCategoriesColumns = VideoCategoriesColumns{
	Id:         "id",
	VideoCount: "video_count",
	Remark:     "remark",
	CreateTime: "create_time",
	UpdateTime: "update_time",
	DeleteTime: "delete_time",
}

// NewVideoCategoriesDao creates and returns a new DAO object for table data access.
func NewVideoCategoriesDao(handlers ...gdb.ModelHandler) *VideoCategoriesDao {
	return &VideoCategoriesDao{
		group:    "default",
		table:    "video_categories",
		columns:  videoCategoriesColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *VideoCategoriesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *VideoCategoriesDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *VideoCategoriesDao) Columns() VideoCategoriesColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *VideoCategoriesDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *VideoCategoriesDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *VideoCategoriesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
