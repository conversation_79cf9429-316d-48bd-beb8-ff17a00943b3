// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// VideoPlaylistRelationsDao is the data access object for the table video_playlist_relations.
type VideoPlaylistRelationsDao struct {
	table    string                        // table is the underlying table name of the DAO.
	group    string                        // group is the database configuration group name of the current DAO.
	columns  VideoPlaylistRelationsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler            // handlers for customized model modification.
}

// VideoPlaylistRelationsColumns defines and stores column names for the table video_playlist_relations.
type VideoPlaylistRelationsColumns struct {
	Id         string // 主键ID
	PlaylistId string // 播放列表ID
	VideoId    string // 视频ID
	VideoName  string // 视频名称
	SortOrder  string // 排序权重，数字越小越靠前
	CreateTime string // 创建时间(毫秒时间戳)
	UpdateTime string // 更新时间(毫秒时间戳)
	DeleteTime string // 删除时间
}

// videoPlaylistRelationsColumns holds the columns for the table video_playlist_relations.
var videoPlaylistRelationsColumns = VideoPlaylistRelationsColumns{
	Id:         "id",
	PlaylistId: "playlist_id",
	VideoId:    "video_id",
	VideoName:  "video_name",
	SortOrder:  "sort_order",
	CreateTime: "create_time",
	UpdateTime: "update_time",
	DeleteTime: "delete_time",
}

// NewVideoPlaylistRelationsDao creates and returns a new DAO object for table data access.
func NewVideoPlaylistRelationsDao(handlers ...gdb.ModelHandler) *VideoPlaylistRelationsDao {
	return &VideoPlaylistRelationsDao{
		group:    "default",
		table:    "video_playlist_relations",
		columns:  videoPlaylistRelationsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *VideoPlaylistRelationsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *VideoPlaylistRelationsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *VideoPlaylistRelationsDao) Columns() VideoPlaylistRelationsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *VideoPlaylistRelationsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *VideoPlaylistRelationsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *VideoPlaylistRelationsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
