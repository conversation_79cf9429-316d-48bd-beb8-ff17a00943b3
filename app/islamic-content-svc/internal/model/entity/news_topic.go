// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// NewsTopic is the golang structure for table news_topic.
type NewsTopic struct {
	Id         uint   `json:"id"         orm:"id"          description:""`                //
	Counts     uint   `json:"counts"     orm:"counts"      description:"文章数量"`            // 文章数量
	IsZh       uint   `json:"isZh"       orm:"is_zh"       description:"是否中文，0-否，1-是"`    // 是否中文，0-否，1-是
	IsEn       uint   `json:"isEn"       orm:"is_en"       description:"是否英文，0-否，1-是"`    // 是否英文，0-否，1-是
	IsId       uint   `json:"isId"       orm:"is_id"       description:"是否印尼文，0-否，1-是"`   // 是否印尼文，0-否，1-是
	Status     uint   `json:"status"     orm:"status"      description:"是否显示，1启用，0关闭"`    // 是否显示，1启用，0关闭
	Sort       uint   `json:"sort"       orm:"sort"        description:"排序，数字越小，排序越靠前"`   // 排序，数字越小，排序越靠前
	AdminId    uint   `json:"adminId"    orm:"admin_id"    description:"分类负责人id"`         // 分类负责人id
	TopicImgs  string `json:"topicImgs"  orm:"topic_imgs"  description:"专题图片"`            // 专题图片
	IsAppShow  int    `json:"isAppShow"  orm:"is_app_show" description:"是否app展示，1：是 0：否"` // 是否app展示，1：是 0：否
	Creater    uint   `json:"creater"    orm:"creater"     description:"创建者id"`           // 创建者id
	CreateName string `json:"createName" orm:"create_name" description:"创建者"`             // 创建者
	CreateTime int64  `json:"createTime" orm:"create_time" description:"创建时间"`            // 创建时间
	UpdateTime int64  `json:"updateTime" orm:"update_time" description:"修改时间"`            // 修改时间
	DeleteTime int64  `json:"deleteTime" orm:"delete_time" description:"删除时间"`            // 删除时间
}
