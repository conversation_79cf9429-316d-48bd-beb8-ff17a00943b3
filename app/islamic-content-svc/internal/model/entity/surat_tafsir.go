// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// SuratTafsir is the golang structure for table surat_tafsir.
type SuratTafsir struct {
	Id          int    `json:"id"          orm:"id"           description:""`          //
	TafsirId    int    `json:"tafsirId"    orm:"tafsir_id"    description:"注释全局ID"`    // 注释全局ID
	SurahId     int    `json:"surahId"     orm:"surah_id"     description:"所属章节ID"`    // 所属章节ID
	AyatNomor   int    `json:"ayatNomor"   orm:"ayat_nomor"   description:"对应经文编号"`    // 对应经文编号
	Tafsir      string `json:"tafsir"      orm:"tafsir"       description:"注释内容"`      // 注释内容
	CreatedTime uint64 `json:"createdTime" orm:"created_time" description:"创建时间戳(毫秒)"` // 创建时间戳(毫秒)
	UpdatedTime uint64 `json:"updatedTime" orm:"updated_time" description:"修改时间戳(毫秒)"` // 修改时间戳(毫秒)
	Wajiz       string `json:"wajiz"       orm:"wajiz"        description:"wajiz 解释"`  // wajiz 解释
}
