// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// FaqQuestion is the golang structure for table faq_question.
type FaqQuestion struct {
	Id            uint   `json:"id"            orm:"id"             description:""`                 //
	FaqCateId     uint   `json:"faqCateId"     orm:"faq_cate_id"    description:""`                 //
	IsOpen        int    `json:"isOpen"        orm:"is_open"        description:"状态 [ 1 启用  2 禁用]"` // 状态 [ 1 启用  2 禁用]
	Sort          int    `json:"sort"          orm:"sort"           description:"排序"`               // 排序
	PublishTime   uint64 `json:"publishTime"   orm:"publish_time"   description:"发布时间"`             // 发布时间
	Views         int    `json:"views"         orm:"views"          description:"浏览量"`              // 浏览量
	CreateAccount string `json:"createAccount" orm:"create_account" description:"创建者"`              // 创建者
	UpdateAccount string `json:"updateAccount" orm:"update_account" description:"更新者"`              // 更新者
	CreateTime    int64  `json:"createTime"    orm:"create_time"    description:""`                 //
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"    description:""`                 //
	DeleteTime    int64  `json:"deleteTime"    orm:"delete_time"    description:""`                 //
	IsZh          uint   `json:"isZh"          orm:"is_zh"          description:"是否中文，0-否，1-是"`     // 是否中文，0-否，1-是
	IsEn          uint   `json:"isEn"          orm:"is_en"          description:"是否英文，0-否，1-是"`     // 是否英文，0-否，1-是
	IsId          uint   `json:"isId"          orm:"is_id"          description:"是否印尼文，0-否，1-是"`    // 是否印尼文，0-否，1-是
}
