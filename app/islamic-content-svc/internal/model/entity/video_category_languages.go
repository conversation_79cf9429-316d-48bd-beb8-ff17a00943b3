// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// VideoCategoryLanguages is the golang structure for table video_category_languages.
type VideoCategoryLanguages struct {
	Id          uint   `json:"id"          orm:"id"          description:"主键ID"`                 // 主键ID
	CategoryId  uint   `json:"categoryId"  orm:"category_id" description:"分类ID"`                 // 分类ID
	LanguageId  uint   `json:"languageId"  orm:"language_id" description:"语言ID：0-中文，1-英文，2-印尼语"` // 语言ID：0-中文，1-英文，2-印尼语
	Name        string `json:"name"        orm:"name"        description:"分类名称"`                 // 分类名称
	Description string `json:"description" orm:"description" description:"分类描述"`                 // 分类描述
	CreateTime  uint64 `json:"createTime"  orm:"create_time" description:"创建时间(毫秒时间戳)"`          // 创建时间(毫秒时间戳)
	UpdateTime  uint64 `json:"updateTime"  orm:"update_time" description:"更新时间(毫秒时间戳)"`          // 更新时间(毫秒时间戳)
	DeleteTime  int64  `json:"deleteTime"  orm:"delete_time" description:"删除时间"`                 // 删除时间
}
