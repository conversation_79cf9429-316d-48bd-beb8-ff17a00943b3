// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// VideoPlaylistLanguages is the golang structure for table video_playlist_languages.
type VideoPlaylistLanguages struct {
	Id          uint   `json:"id"          orm:"id"          description:"主键ID"`                 // 主键ID
	PlaylistId  uint   `json:"playlistId"  orm:"playlist_id" description:"播放列表ID"`               // 播放列表ID
	LanguageId  uint   `json:"languageId"  orm:"language_id" description:"语言ID：0-中文，1-英文，2-印尼语"` // 语言ID：0-中文，1-英文，2-印尼语
	Name        string `json:"name"        orm:"name"        description:"播放列表名称"`               // 播放列表名称
	ShortTitle  string `json:"shortTitle"  orm:"short_title" description:"播放列表短标题"`              // 播放列表短标题
	Description string `json:"description" orm:"description" description:"播放列表描述"`               // 播放列表描述
	CreateTime  uint64 `json:"createTime"  orm:"create_time" description:"创建时间(毫秒时间戳)"`          // 创建时间(毫秒时间戳)
	UpdateTime  uint64 `json:"updateTime"  orm:"update_time" description:"更新时间(毫秒时间戳)"`          // 更新时间(毫秒时间戳)
	DeleteTime  int64  `json:"deleteTime"  orm:"delete_time" description:"删除时间"`                 // 删除时间
}
