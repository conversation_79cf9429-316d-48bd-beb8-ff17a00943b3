// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package v1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// UserServiceClient is the client API for UserService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserServiceClient interface {
	// 发送验证码（支持短信和 WhatsApp）
	// 适用于登录、注册、找回密码等场景。
	// 限制频率：一个手机号每 60 秒只能请求一次。
	// POST /api/user-account/user/v1/UserService/SendVerifyCode
	SendVerifyCode(ctx context.Context, in *SendVerifyCodeReq, opts ...grpc.CallOption) (*SendVerifyCodeRes, error)
	// 验证登录手机号
	// 适用于更换手机号场景。
	// 限制频率：一个手机号每 60 秒只能请求一次。
	// POST /api/user-account/user/v1/UserService/VerifyCode
	VerifyCode(ctx context.Context, in *VerifyCodeReq, opts ...grpc.CallOption) (*VerifyCodeRes, error)
	// 用户注册
	SignUp(ctx context.Context, in *SignUpReq, opts ...grpc.CallOption) (*SignUpRes, error)
	// 用户登录
	SignIn(ctx context.Context, in *SignInReq, opts ...grpc.CallOption) (*UserSignInRes, error)
	// 用户账号密码登录
	SignInByAccount(ctx context.Context, in *SignInByAccountReq, opts ...grpc.CallOption) (*SignInByAccountRes, error)
	// 手机号短信验证码登录
	// POST /api/user-account/user/v1/UserService/SignIn
	SignInByPhone(ctx context.Context, in *SignInByPhoneReq, opts ...grpc.CallOption) (*SignInByPhoneRes, error)
	// 手机号检查（判断是否为虚拟号/黑名单）
	PhoneValidCheck(ctx context.Context, in *PhoneValidCheckReq, opts ...grpc.CallOption) (*PhoneValidCheckRes, error)
	// 刷新token
	// POST /api/user-account/user/v1/UserService/RefreshToken
	RefreshToken(ctx context.Context, in *RefreshTokenReq, opts ...grpc.CallOption) (*RefreshTokenRes, error)
	// 获取用户信息
	// GET /api/user-account/user/v1/UserService/UserInfo
	UserInfo(ctx context.Context, in *UserInfoReq, opts ...grpc.CallOption) (*UserInfoRes, error)
	// 更新用户信息
	// POST /api/user-account/user/v1/UserService/UpdateUserInfo
	UpdateUserInfo(ctx context.Context, in *UpdateUserInfoReq, opts ...grpc.CallOption) (*UpdateUserInfoRes, error)
	// 获取默认头像列表
	// GET /api/user-account/user/v1/UserService/AvatarList
	AvatarList(ctx context.Context, in *AvatarListReq, opts ...grpc.CallOption) (*AvatarListRes, error)
	// 更换手机号码
	// POST /api/user-account/user/v1/UserService/ChangePhone
	ChangePhone(ctx context.Context, in *ChangePhoneReq, opts ...grpc.CallOption) (*ChangePhoneRes, error)
}

type userServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserServiceClient(cc grpc.ClientConnInterface) UserServiceClient {
	return &userServiceClient{cc}
}

func (c *userServiceClient) SendVerifyCode(ctx context.Context, in *SendVerifyCodeReq, opts ...grpc.CallOption) (*SendVerifyCodeRes, error) {
	out := new(SendVerifyCodeRes)
	err := c.cc.Invoke(ctx, "/user.v1.UserService/SendVerifyCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) VerifyCode(ctx context.Context, in *VerifyCodeReq, opts ...grpc.CallOption) (*VerifyCodeRes, error) {
	out := new(VerifyCodeRes)
	err := c.cc.Invoke(ctx, "/user.v1.UserService/VerifyCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) SignUp(ctx context.Context, in *SignUpReq, opts ...grpc.CallOption) (*SignUpRes, error) {
	out := new(SignUpRes)
	err := c.cc.Invoke(ctx, "/user.v1.UserService/SignUp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) SignIn(ctx context.Context, in *SignInReq, opts ...grpc.CallOption) (*UserSignInRes, error) {
	out := new(UserSignInRes)
	err := c.cc.Invoke(ctx, "/user.v1.UserService/SignIn", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) SignInByAccount(ctx context.Context, in *SignInByAccountReq, opts ...grpc.CallOption) (*SignInByAccountRes, error) {
	out := new(SignInByAccountRes)
	err := c.cc.Invoke(ctx, "/user.v1.UserService/SignInByAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) SignInByPhone(ctx context.Context, in *SignInByPhoneReq, opts ...grpc.CallOption) (*SignInByPhoneRes, error) {
	out := new(SignInByPhoneRes)
	err := c.cc.Invoke(ctx, "/user.v1.UserService/SignInByPhone", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) PhoneValidCheck(ctx context.Context, in *PhoneValidCheckReq, opts ...grpc.CallOption) (*PhoneValidCheckRes, error) {
	out := new(PhoneValidCheckRes)
	err := c.cc.Invoke(ctx, "/user.v1.UserService/PhoneValidCheck", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) RefreshToken(ctx context.Context, in *RefreshTokenReq, opts ...grpc.CallOption) (*RefreshTokenRes, error) {
	out := new(RefreshTokenRes)
	err := c.cc.Invoke(ctx, "/user.v1.UserService/RefreshToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) UserInfo(ctx context.Context, in *UserInfoReq, opts ...grpc.CallOption) (*UserInfoRes, error) {
	out := new(UserInfoRes)
	err := c.cc.Invoke(ctx, "/user.v1.UserService/UserInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) UpdateUserInfo(ctx context.Context, in *UpdateUserInfoReq, opts ...grpc.CallOption) (*UpdateUserInfoRes, error) {
	out := new(UpdateUserInfoRes)
	err := c.cc.Invoke(ctx, "/user.v1.UserService/UpdateUserInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) AvatarList(ctx context.Context, in *AvatarListReq, opts ...grpc.CallOption) (*AvatarListRes, error) {
	out := new(AvatarListRes)
	err := c.cc.Invoke(ctx, "/user.v1.UserService/AvatarList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) ChangePhone(ctx context.Context, in *ChangePhoneReq, opts ...grpc.CallOption) (*ChangePhoneRes, error) {
	out := new(ChangePhoneRes)
	err := c.cc.Invoke(ctx, "/user.v1.UserService/ChangePhone", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserServiceServer is the server API for UserService service.
// All implementations must embed UnimplementedUserServiceServer
// for forward compatibility
type UserServiceServer interface {
	// 发送验证码（支持短信和 WhatsApp）
	// 适用于登录、注册、找回密码等场景。
	// 限制频率：一个手机号每 60 秒只能请求一次。
	// POST /api/user-account/user/v1/UserService/SendVerifyCode
	SendVerifyCode(context.Context, *SendVerifyCodeReq) (*SendVerifyCodeRes, error)
	// 验证登录手机号
	// 适用于更换手机号场景。
	// 限制频率：一个手机号每 60 秒只能请求一次。
	// POST /api/user-account/user/v1/UserService/VerifyCode
	VerifyCode(context.Context, *VerifyCodeReq) (*VerifyCodeRes, error)
	// 用户注册
	SignUp(context.Context, *SignUpReq) (*SignUpRes, error)
	// 用户登录
	SignIn(context.Context, *SignInReq) (*UserSignInRes, error)
	// 用户账号密码登录
	SignInByAccount(context.Context, *SignInByAccountReq) (*SignInByAccountRes, error)
	// 手机号短信验证码登录
	// POST /api/user-account/user/v1/UserService/SignIn
	SignInByPhone(context.Context, *SignInByPhoneReq) (*SignInByPhoneRes, error)
	// 手机号检查（判断是否为虚拟号/黑名单）
	PhoneValidCheck(context.Context, *PhoneValidCheckReq) (*PhoneValidCheckRes, error)
	// 刷新token
	// POST /api/user-account/user/v1/UserService/RefreshToken
	RefreshToken(context.Context, *RefreshTokenReq) (*RefreshTokenRes, error)
	// 获取用户信息
	// GET /api/user-account/user/v1/UserService/UserInfo
	UserInfo(context.Context, *UserInfoReq) (*UserInfoRes, error)
	// 更新用户信息
	// POST /api/user-account/user/v1/UserService/UpdateUserInfo
	UpdateUserInfo(context.Context, *UpdateUserInfoReq) (*UpdateUserInfoRes, error)
	// 获取默认头像列表
	// GET /api/user-account/user/v1/UserService/AvatarList
	AvatarList(context.Context, *AvatarListReq) (*AvatarListRes, error)
	// 更换手机号码
	// POST /api/user-account/user/v1/UserService/ChangePhone
	ChangePhone(context.Context, *ChangePhoneReq) (*ChangePhoneRes, error)
	mustEmbedUnimplementedUserServiceServer()
}

// UnimplementedUserServiceServer must be embedded to have forward compatible implementations.
type UnimplementedUserServiceServer struct {
}

func (UnimplementedUserServiceServer) SendVerifyCode(context.Context, *SendVerifyCodeReq) (*SendVerifyCodeRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendVerifyCode not implemented")
}
func (UnimplementedUserServiceServer) VerifyCode(context.Context, *VerifyCodeReq) (*VerifyCodeRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyCode not implemented")
}
func (UnimplementedUserServiceServer) SignUp(context.Context, *SignUpReq) (*SignUpRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignUp not implemented")
}
func (UnimplementedUserServiceServer) SignIn(context.Context, *SignInReq) (*UserSignInRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignIn not implemented")
}
func (UnimplementedUserServiceServer) SignInByAccount(context.Context, *SignInByAccountReq) (*SignInByAccountRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignInByAccount not implemented")
}
func (UnimplementedUserServiceServer) SignInByPhone(context.Context, *SignInByPhoneReq) (*SignInByPhoneRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignInByPhone not implemented")
}
func (UnimplementedUserServiceServer) PhoneValidCheck(context.Context, *PhoneValidCheckReq) (*PhoneValidCheckRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PhoneValidCheck not implemented")
}
func (UnimplementedUserServiceServer) RefreshToken(context.Context, *RefreshTokenReq) (*RefreshTokenRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshToken not implemented")
}
func (UnimplementedUserServiceServer) UserInfo(context.Context, *UserInfoReq) (*UserInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserInfo not implemented")
}
func (UnimplementedUserServiceServer) UpdateUserInfo(context.Context, *UpdateUserInfoReq) (*UpdateUserInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserInfo not implemented")
}
func (UnimplementedUserServiceServer) AvatarList(context.Context, *AvatarListReq) (*AvatarListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AvatarList not implemented")
}
func (UnimplementedUserServiceServer) ChangePhone(context.Context, *ChangePhoneReq) (*ChangePhoneRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangePhone not implemented")
}
func (UnimplementedUserServiceServer) mustEmbedUnimplementedUserServiceServer() {}

// UnsafeUserServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserServiceServer will
// result in compilation errors.
type UnsafeUserServiceServer interface {
	mustEmbedUnimplementedUserServiceServer()
}

func RegisterUserServiceServer(s *grpc.Server, srv UserServiceServer) {
	s.RegisterService(&_UserService_serviceDesc, srv)
}

func _UserService_SendVerifyCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendVerifyCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).SendVerifyCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.v1.UserService/SendVerifyCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).SendVerifyCode(ctx, req.(*SendVerifyCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_VerifyCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).VerifyCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.v1.UserService/VerifyCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).VerifyCode(ctx, req.(*VerifyCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_SignUp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignUpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).SignUp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.v1.UserService/SignUp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).SignUp(ctx, req.(*SignUpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_SignIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignInReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).SignIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.v1.UserService/SignIn",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).SignIn(ctx, req.(*SignInReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_SignInByAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignInByAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).SignInByAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.v1.UserService/SignInByAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).SignInByAccount(ctx, req.(*SignInByAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_SignInByPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignInByPhoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).SignInByPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.v1.UserService/SignInByPhone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).SignInByPhone(ctx, req.(*SignInByPhoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_PhoneValidCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PhoneValidCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).PhoneValidCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.v1.UserService/PhoneValidCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).PhoneValidCheck(ctx, req.(*PhoneValidCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_RefreshToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).RefreshToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.v1.UserService/RefreshToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).RefreshToken(ctx, req.(*RefreshTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_UserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).UserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.v1.UserService/UserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).UserInfo(ctx, req.(*UserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_UpdateUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).UpdateUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.v1.UserService/UpdateUserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).UpdateUserInfo(ctx, req.(*UpdateUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_AvatarList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AvatarListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).AvatarList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.v1.UserService/AvatarList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).AvatarList(ctx, req.(*AvatarListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_ChangePhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangePhoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).ChangePhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.v1.UserService/ChangePhone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).ChangePhone(ctx, req.(*ChangePhoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user.v1.UserService",
	HandlerType: (*UserServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendVerifyCode",
			Handler:    _UserService_SendVerifyCode_Handler,
		},
		{
			MethodName: "VerifyCode",
			Handler:    _UserService_VerifyCode_Handler,
		},
		{
			MethodName: "SignUp",
			Handler:    _UserService_SignUp_Handler,
		},
		{
			MethodName: "SignIn",
			Handler:    _UserService_SignIn_Handler,
		},
		{
			MethodName: "SignInByAccount",
			Handler:    _UserService_SignInByAccount_Handler,
		},
		{
			MethodName: "SignInByPhone",
			Handler:    _UserService_SignInByPhone_Handler,
		},
		{
			MethodName: "PhoneValidCheck",
			Handler:    _UserService_PhoneValidCheck_Handler,
		},
		{
			MethodName: "RefreshToken",
			Handler:    _UserService_RefreshToken_Handler,
		},
		{
			MethodName: "UserInfo",
			Handler:    _UserService_UserInfo_Handler,
		},
		{
			MethodName: "UpdateUserInfo",
			Handler:    _UserService_UpdateUserInfo_Handler,
		},
		{
			MethodName: "AvatarList",
			Handler:    _UserService_AvatarList_Handler,
		},
		{
			MethodName: "ChangePhone",
			Handler:    _UserService_ChangePhone_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "user/v1/user.proto",
}
