// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package v1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// NuIdServiceClient is the client API for NuIdService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NuIdServiceClient interface {
	// 获取NU区域列表
	// Get /api/user-account/nu/v1/NuIdService/GetWilayahNUList
	GetWilayahNUList(ctx context.Context, in *GetWilayahNUListReq, opts ...grpc.CallOption) (*GetWilayahNUListRes, error)
	// 提交NU认证申请
	// POST /api/user-account/nu/v1/NuIdService/SubmitNuId
	SubmitNuId(ctx context.Context, in *SubmitNuIdReq, opts ...grpc.CallOption) (*SubmitNuIdRes, error)
	// 查询NU认证信息
	// Get /api/user-account/nu/v1/NuIdService/GetNuIdInfo
	GetNuIdInfo(ctx context.Context, in *GetNuIdInfoReq, opts ...grpc.CallOption) (*GetNuIdInfoRes, error)
}

type nuIdServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNuIdServiceClient(cc grpc.ClientConnInterface) NuIdServiceClient {
	return &nuIdServiceClient{cc}
}

func (c *nuIdServiceClient) GetWilayahNUList(ctx context.Context, in *GetWilayahNUListReq, opts ...grpc.CallOption) (*GetWilayahNUListRes, error) {
	out := new(GetWilayahNUListRes)
	err := c.cc.Invoke(ctx, "/nu.v1.NuIdService/GetWilayahNUList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nuIdServiceClient) SubmitNuId(ctx context.Context, in *SubmitNuIdReq, opts ...grpc.CallOption) (*SubmitNuIdRes, error) {
	out := new(SubmitNuIdRes)
	err := c.cc.Invoke(ctx, "/nu.v1.NuIdService/SubmitNuId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nuIdServiceClient) GetNuIdInfo(ctx context.Context, in *GetNuIdInfoReq, opts ...grpc.CallOption) (*GetNuIdInfoRes, error) {
	out := new(GetNuIdInfoRes)
	err := c.cc.Invoke(ctx, "/nu.v1.NuIdService/GetNuIdInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NuIdServiceServer is the server API for NuIdService service.
// All implementations must embed UnimplementedNuIdServiceServer
// for forward compatibility
type NuIdServiceServer interface {
	// 获取NU区域列表
	// Get /api/user-account/nu/v1/NuIdService/GetWilayahNUList
	GetWilayahNUList(context.Context, *GetWilayahNUListReq) (*GetWilayahNUListRes, error)
	// 提交NU认证申请
	// POST /api/user-account/nu/v1/NuIdService/SubmitNuId
	SubmitNuId(context.Context, *SubmitNuIdReq) (*SubmitNuIdRes, error)
	// 查询NU认证信息
	// Get /api/user-account/nu/v1/NuIdService/GetNuIdInfo
	GetNuIdInfo(context.Context, *GetNuIdInfoReq) (*GetNuIdInfoRes, error)
	mustEmbedUnimplementedNuIdServiceServer()
}

// UnimplementedNuIdServiceServer must be embedded to have forward compatible implementations.
type UnimplementedNuIdServiceServer struct {
}

func (UnimplementedNuIdServiceServer) GetWilayahNUList(context.Context, *GetWilayahNUListReq) (*GetWilayahNUListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWilayahNUList not implemented")
}
func (UnimplementedNuIdServiceServer) SubmitNuId(context.Context, *SubmitNuIdReq) (*SubmitNuIdRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitNuId not implemented")
}
func (UnimplementedNuIdServiceServer) GetNuIdInfo(context.Context, *GetNuIdInfoReq) (*GetNuIdInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNuIdInfo not implemented")
}
func (UnimplementedNuIdServiceServer) mustEmbedUnimplementedNuIdServiceServer() {}

// UnsafeNuIdServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NuIdServiceServer will
// result in compilation errors.
type UnsafeNuIdServiceServer interface {
	mustEmbedUnimplementedNuIdServiceServer()
}

func RegisterNuIdServiceServer(s *grpc.Server, srv NuIdServiceServer) {
	s.RegisterService(&_NuIdService_serviceDesc, srv)
}

func _NuIdService_GetWilayahNUList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWilayahNUListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NuIdServiceServer).GetWilayahNUList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nu.v1.NuIdService/GetWilayahNUList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NuIdServiceServer).GetWilayahNUList(ctx, req.(*GetWilayahNUListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NuIdService_SubmitNuId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitNuIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NuIdServiceServer).SubmitNuId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nu.v1.NuIdService/SubmitNuId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NuIdServiceServer).SubmitNuId(ctx, req.(*SubmitNuIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NuIdService_GetNuIdInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNuIdInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NuIdServiceServer).GetNuIdInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nu.v1.NuIdService/GetNuIdInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NuIdServiceServer).GetNuIdInfo(ctx, req.(*GetNuIdInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _NuIdService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "nu.v1.NuIdService",
	HandlerType: (*NuIdServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetWilayahNUList",
			Handler:    _NuIdService_GetWilayahNUList_Handler,
		},
		{
			MethodName: "SubmitNuId",
			Handler:    _NuIdService_SubmitNuId_Handler,
		},
		{
			MethodName: "GetNuIdInfo",
			Handler:    _NuIdService_GetNuIdInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "nu/v1/nu_id.proto",
}
