// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: nu/v1/nu_id.proto

package v1

import (
	common "halalplus/api/common"
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 性别
type Gender int32

const (
	Gender_UNKNOWN Gender = 0
	Gender_MALE    Gender = 1
	Gender_FEMALE  Gender = 2
)

// Enum value maps for Gender.
var (
	Gender_name = map[int32]string{
		0: "UNKNOWN",
		1: "MALE",
		2: "FEMALE",
	}
	Gender_value = map[string]int32{
		"UNKNOWN": 0,
		"MALE":    1,
		"FEMALE":  2,
	}
)

func (x Gender) Enum() *Gender {
	p := new(Gender)
	*p = x
	return p
}

func (x Gender) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Gender) Descriptor() protoreflect.EnumDescriptor {
	return file_nu_v1_nu_id_proto_enumTypes[0].Descriptor()
}

func (Gender) Type() protoreflect.EnumType {
	return &file_nu_v1_nu_id_proto_enumTypes[0]
}

func (x Gender) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Gender.Descriptor instead.
func (Gender) EnumDescriptor() ([]byte, []int) {
	return file_nu_v1_nu_id_proto_rawDescGZIP(), []int{0}
}

// 认证状态
type NuIdStatus int32

const (
	NuIdStatus_UNSPECIFIED NuIdStatus = 0 // 未指定（默认值）
	NuIdStatus_PENDING     NuIdStatus = 1 // 待审核
	NuIdStatus_APPROVED    NuIdStatus = 2 // 已通过
	NuIdStatus_REJECTED    NuIdStatus = 3 // 已驳回
)

// Enum value maps for NuIdStatus.
var (
	NuIdStatus_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "PENDING",
		2: "APPROVED",
		3: "REJECTED",
	}
	NuIdStatus_value = map[string]int32{
		"UNSPECIFIED": 0,
		"PENDING":     1,
		"APPROVED":    2,
		"REJECTED":    3,
	}
)

func (x NuIdStatus) Enum() *NuIdStatus {
	p := new(NuIdStatus)
	*p = x
	return p
}

func (x NuIdStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NuIdStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_nu_v1_nu_id_proto_enumTypes[1].Descriptor()
}

func (NuIdStatus) Type() protoreflect.EnumType {
	return &file_nu_v1_nu_id_proto_enumTypes[1]
}

func (x NuIdStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NuIdStatus.Descriptor instead.
func (NuIdStatus) EnumDescriptor() ([]byte, []int) {
	return file_nu_v1_nu_id_proto_rawDescGZIP(), []int{1}
}

type WilayahNU struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"地区 ID，例如 'W001'"`                      // 地区 ID，例如 "W001"
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" dc:"地区名称，例如 'Nahdlatul Ulama Jakarta'"` // 地区名称，例如 "Nahdlatul Ulama Jakarta"
}

func (x *WilayahNU) Reset() {
	*x = WilayahNU{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nu_v1_nu_id_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WilayahNU) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WilayahNU) ProtoMessage() {}

func (x *WilayahNU) ProtoReflect() protoreflect.Message {
	mi := &file_nu_v1_nu_id_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WilayahNU.ProtoReflect.Descriptor instead.
func (*WilayahNU) Descriptor() ([]byte, []int) {
	return file_nu_v1_nu_id_proto_rawDescGZIP(), []int{0}
}

func (x *WilayahNU) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WilayahNU) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// 请求：获取 Wilayah NU 列表
type GetWilayahNUListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetWilayahNUListReq) Reset() {
	*x = GetWilayahNUListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nu_v1_nu_id_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWilayahNUListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWilayahNUListReq) ProtoMessage() {}

func (x *GetWilayahNUListReq) ProtoReflect() protoreflect.Message {
	mi := &file_nu_v1_nu_id_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWilayahNUListReq.ProtoReflect.Descriptor instead.
func (*GetWilayahNUListReq) Descriptor() ([]byte, []int) {
	return file_nu_v1_nu_id_proto_rawDescGZIP(), []int{1}
}

type GetWilayahNUListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*WilayahNU `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *GetWilayahNUListResData) Reset() {
	*x = GetWilayahNUListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nu_v1_nu_id_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWilayahNUListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWilayahNUListResData) ProtoMessage() {}

func (x *GetWilayahNUListResData) ProtoReflect() protoreflect.Message {
	mi := &file_nu_v1_nu_id_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWilayahNUListResData.ProtoReflect.Descriptor instead.
func (*GetWilayahNUListResData) Descriptor() ([]byte, []int) {
	return file_nu_v1_nu_id_proto_rawDescGZIP(), []int{2}
}

func (x *GetWilayahNUListResData) GetList() []*WilayahNU {
	if x != nil {
		return x.List
	}
	return nil
}

type GetWilayahNUListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error            `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *GetWilayahNUListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetWilayahNUListRes) Reset() {
	*x = GetWilayahNUListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nu_v1_nu_id_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWilayahNUListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWilayahNUListRes) ProtoMessage() {}

func (x *GetWilayahNUListRes) ProtoReflect() protoreflect.Message {
	mi := &file_nu_v1_nu_id_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWilayahNUListRes.ProtoReflect.Descriptor instead.
func (*GetWilayahNUListRes) Descriptor() ([]byte, []int) {
	return file_nu_v1_nu_id_proto_rawDescGZIP(), []int{3}
}

func (x *GetWilayahNUListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetWilayahNUListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetWilayahNUListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *GetWilayahNUListRes) GetData() *GetWilayahNUListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetNuIdInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetNuIdInfoReq) Reset() {
	*x = GetNuIdInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nu_v1_nu_id_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNuIdInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNuIdInfoReq) ProtoMessage() {}

func (x *GetNuIdInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_nu_v1_nu_id_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNuIdInfoReq.ProtoReflect.Descriptor instead.
func (*GetNuIdInfoReq) Descriptor() ([]byte, []int) {
	return file_nu_v1_nu_id_proto_rawDescGZIP(), []int{4}
}

// 用户的NU认证信息
type NuIdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          uint64     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"主键ID"`                                                                                                  // 主键ID
	Status      NuIdStatus `protobuf:"varint,2,opt,name=status,proto3,enum=nu.v1.NuIdStatus" json:"status,omitempty" dc:"认证状态"`                                                                    // 认证状态
	RealName    string     `protobuf:"bytes,3,opt,name=real_name,json=realName,proto3" json:"real_name,omitempty" dc:"用户真实姓名"`                                                                     // 用户真实姓名
	Gender      Gender     `protobuf:"varint,4,opt,name=gender,proto3,enum=nu.v1.Gender" json:"gender,omitempty" dc:"性别"`                                                                          // 性别
	BirthDate   string     `protobuf:"bytes,5,opt,name=birth_date,json=birthDate,proto3" json:"birth_date,omitempty" dc:"出生年月日,格式2006-01-02"`                                                      // 出生年月日,格式2006-01-02
	NuIdNumber  string     `protobuf:"bytes,6,opt,name=nu_id_number,json=nuIdNumber,proto3" json:"nu_id_number,omitempty" dc:"NU会员编号"`                                                             // NU会员编号
	WilayahPwnu string     `protobuf:"bytes,7,opt,name=wilayah_pwnu,json=wilayahPwnu,proto3" json:"wilayah_pwnu,omitempty" dc:"所属PWNU地区,nu地址，从nu_wilayah_pwnu选择，设置为WilayahNU.name, 例如'PWNU Aceh'"` // 所属PWNU地区,nu地址，从nu_wilayah_pwnu选择，设置为WilayahNU.name, 例如"PWNU Aceh"
	CertFileUrl string     `protobuf:"bytes,8,opt,name=cert_file_url,json=certFileUrl,proto3" json:"cert_file_url,omitempty" dc:"NU认证材料图片URL（证件照/证书）"`                                             // NU认证材料图片URL（证件照/证书）
}

func (x *NuIdInfo) Reset() {
	*x = NuIdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nu_v1_nu_id_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NuIdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NuIdInfo) ProtoMessage() {}

func (x *NuIdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_nu_v1_nu_id_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NuIdInfo.ProtoReflect.Descriptor instead.
func (*NuIdInfo) Descriptor() ([]byte, []int) {
	return file_nu_v1_nu_id_proto_rawDescGZIP(), []int{5}
}

func (x *NuIdInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *NuIdInfo) GetStatus() NuIdStatus {
	if x != nil {
		return x.Status
	}
	return NuIdStatus_UNSPECIFIED
}

func (x *NuIdInfo) GetRealName() string {
	if x != nil {
		return x.RealName
	}
	return ""
}

func (x *NuIdInfo) GetGender() Gender {
	if x != nil {
		return x.Gender
	}
	return Gender_UNKNOWN
}

func (x *NuIdInfo) GetBirthDate() string {
	if x != nil {
		return x.BirthDate
	}
	return ""
}

func (x *NuIdInfo) GetNuIdNumber() string {
	if x != nil {
		return x.NuIdNumber
	}
	return ""
}

func (x *NuIdInfo) GetWilayahPwnu() string {
	if x != nil {
		return x.WilayahPwnu
	}
	return ""
}

func (x *NuIdInfo) GetCertFileUrl() string {
	if x != nil {
		return x.CertFileUrl
	}
	return ""
}

type GetNuIdInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *NuIdInfo     `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetNuIdInfoRes) Reset() {
	*x = GetNuIdInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nu_v1_nu_id_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNuIdInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNuIdInfoRes) ProtoMessage() {}

func (x *GetNuIdInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_nu_v1_nu_id_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNuIdInfoRes.ProtoReflect.Descriptor instead.
func (*GetNuIdInfoRes) Descriptor() ([]byte, []int) {
	return file_nu_v1_nu_id_proto_rawDescGZIP(), []int{6}
}

func (x *GetNuIdInfoRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetNuIdInfoRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetNuIdInfoRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *GetNuIdInfoRes) GetData() *NuIdInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

// 提交认证
// 尚未审核通过的都可以重复提交
// 审核通过后(status=APPROVED)，后台禁止修改
type SubmitNuIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RealName    string `protobuf:"bytes,3,opt,name=real_name,json=realName,proto3" json:"real_name,omitempty" dc:"用户真实姓名"`                                      // 用户真实姓名
	Gender      Gender `protobuf:"varint,4,opt,name=gender,proto3,enum=nu.v1.Gender" json:"gender,omitempty" dc:"性别：0未知 1男 2女"`                                 // 性别：0未知 1男 2女
	BirthDate   string `protobuf:"bytes,5,opt,name=birth_date,json=birthDate,proto3" json:"birth_date,omitempty" dc:"出生年月日,格式2006-01-02"`                       // 出生年月日,格式2006-01-02
	NuIdNumber  string `protobuf:"bytes,6,opt,name=nu_id_number,json=nuIdNumber,proto3" json:"nu_id_number,omitempty" dc:"NU会员编号，例如 NU-2024-JAWA-001"`          // NU会员编号，例如 NU-2024-JAWA-001
	WilayahPwnu string `protobuf:"bytes,7,opt,name=wilayah_pwnu,json=wilayahPwnu,proto3" json:"wilayah_pwnu,omitempty" dc:"所属PWNU地区,nu地址，从nu_wilayah_pwnu选择"`   // 所属PWNU地区,nu地址，从nu_wilayah_pwnu选择
	CertFileUrl string `protobuf:"bytes,8,opt,name=cert_file_url,json=certFileUrl,proto3" json:"cert_file_url,omitempty" dc:"NU认证材料图片URL（证件照/证书）, 请使用上传文件接口上传"` // NU认证材料图片URL（证件照/证书）, 请使用上传文件接口上传
}

func (x *SubmitNuIdReq) Reset() {
	*x = SubmitNuIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nu_v1_nu_id_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitNuIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitNuIdReq) ProtoMessage() {}

func (x *SubmitNuIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_nu_v1_nu_id_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitNuIdReq.ProtoReflect.Descriptor instead.
func (*SubmitNuIdReq) Descriptor() ([]byte, []int) {
	return file_nu_v1_nu_id_proto_rawDescGZIP(), []int{7}
}

func (x *SubmitNuIdReq) GetRealName() string {
	if x != nil {
		return x.RealName
	}
	return ""
}

func (x *SubmitNuIdReq) GetGender() Gender {
	if x != nil {
		return x.Gender
	}
	return Gender_UNKNOWN
}

func (x *SubmitNuIdReq) GetBirthDate() string {
	if x != nil {
		return x.BirthDate
	}
	return ""
}

func (x *SubmitNuIdReq) GetNuIdNumber() string {
	if x != nil {
		return x.NuIdNumber
	}
	return ""
}

func (x *SubmitNuIdReq) GetWilayahPwnu() string {
	if x != nil {
		return x.WilayahPwnu
	}
	return ""
}

func (x *SubmitNuIdReq) GetCertFileUrl() string {
	if x != nil {
		return x.CertFileUrl
	}
	return ""
}

type SubmitNuIdRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *SubmitNuIdRes) Reset() {
	*x = SubmitNuIdRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nu_v1_nu_id_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitNuIdRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitNuIdRes) ProtoMessage() {}

func (x *SubmitNuIdRes) ProtoReflect() protoreflect.Message {
	mi := &file_nu_v1_nu_id_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitNuIdRes.ProtoReflect.Descriptor instead.
func (*SubmitNuIdRes) Descriptor() ([]byte, []int) {
	return file_nu_v1_nu_id_proto_rawDescGZIP(), []int{8}
}

func (x *SubmitNuIdRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SubmitNuIdRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SubmitNuIdRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_nu_v1_nu_id_proto protoreflect.FileDescriptor

var file_nu_v1_nu_id_proto_rawDesc = []byte{
	0x0a, 0x11, 0x6e, 0x75, 0x2f, 0x76, 0x31, 0x2f, 0x6e, 0x75, 0x5f, 0x69, 0x64, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x05, 0x6e, 0x75, 0x2e, 0x76, 0x31, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x2f, 0x0a,
	0x09, 0x57, 0x69, 0x6c, 0x61, 0x79, 0x61, 0x68, 0x4e, 0x55, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x15,
	0x0a, 0x13, 0x47, 0x65, 0x74, 0x57, 0x69, 0x6c, 0x61, 0x79, 0x61, 0x68, 0x4e, 0x55, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x22, 0x3f, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x57, 0x69, 0x6c, 0x61,
	0x79, 0x61, 0x68, 0x4e, 0x55, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x24, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x6e, 0x75, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x69, 0x6c, 0x61, 0x79, 0x61, 0x68, 0x4e, 0x55,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x94, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x57, 0x69,
	0x6c, 0x61, 0x79, 0x61, 0x68, 0x4e, 0x55, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x32, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6e, 0x75, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x57, 0x69, 0x6c, 0x61, 0x79, 0x61, 0x68, 0x4e, 0x55, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x10, 0x0a,
	0x0e, 0x47, 0x65, 0x74, 0x4e, 0x75, 0x49, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x22,
	0x91, 0x02, 0x0a, 0x08, 0x4e, 0x75, 0x49, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x29, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x6e,
	0x75, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x75, 0x49, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x61, 0x6c, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x61, 0x6c,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x6e, 0x75, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x62,
	0x69, 0x72, 0x74, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x62, 0x69, 0x72, 0x74, 0x68, 0x44, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x6e, 0x75,
	0x5f, 0x69, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6e, 0x75, 0x49, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c,
	0x77, 0x69, 0x6c, 0x61, 0x79, 0x61, 0x68, 0x5f, 0x70, 0x77, 0x6e, 0x75, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x77, 0x69, 0x6c, 0x61, 0x79, 0x61, 0x68, 0x50, 0x77, 0x6e, 0x75, 0x12,
	0x22, 0x0a, 0x0d, 0x63, 0x65, 0x72, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x65, 0x72, 0x74, 0x46, 0x69, 0x6c, 0x65,
	0x55, 0x72, 0x6c, 0x22, 0x80, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x4e, 0x75, 0x49, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x12, 0x23, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0f, 0x2e, 0x6e, 0x75, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x75, 0x49, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xdb, 0x01, 0x0a, 0x0d, 0x53, 0x75, 0x62, 0x6d, 0x69,
	0x74, 0x4e, 0x75, 0x49, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x61, 0x6c,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x61,
	0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x6e, 0x75, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a,
	0x62, 0x69, 0x72, 0x74, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x62, 0x69, 0x72, 0x74, 0x68, 0x44, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x6e,
	0x75, 0x5f, 0x69, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6e, 0x75, 0x49, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x21, 0x0a,
	0x0c, 0x77, 0x69, 0x6c, 0x61, 0x79, 0x61, 0x68, 0x5f, 0x70, 0x77, 0x6e, 0x75, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x69, 0x6c, 0x61, 0x79, 0x61, 0x68, 0x50, 0x77, 0x6e, 0x75,
	0x12, 0x22, 0x0a, 0x0d, 0x63, 0x65, 0x72, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x65, 0x72, 0x74, 0x46, 0x69, 0x6c,
	0x65, 0x55, 0x72, 0x6c, 0x22, 0x5a, 0x0a, 0x0d, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x4e, 0x75,
	0x49, 0x64, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x2a, 0x2b, 0x0a, 0x06, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e,
	0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x4d, 0x41, 0x4c, 0x45, 0x10,
	0x01, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x45, 0x4d, 0x41, 0x4c, 0x45, 0x10, 0x02, 0x2a, 0x46, 0x0a,
	0x0a, 0x4e, 0x75, 0x49, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0f, 0x0a, 0x0b, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07,
	0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x50, 0x50,
	0x52, 0x4f, 0x56, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x45, 0x4a, 0x45, 0x43,
	0x54, 0x45, 0x44, 0x10, 0x03, 0x32, 0xd0, 0x01, 0x0a, 0x0b, 0x4e, 0x75, 0x49, 0x64, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4a, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x57, 0x69, 0x6c, 0x61,
	0x79, 0x61, 0x68, 0x4e, 0x55, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x2e, 0x6e, 0x75, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x69, 0x6c, 0x61, 0x79, 0x61, 0x68, 0x4e, 0x55, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x6e, 0x75, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x57, 0x69, 0x6c, 0x61, 0x79, 0x61, 0x68, 0x4e, 0x55, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x12, 0x38, 0x0a, 0x0a, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x4e, 0x75, 0x49, 0x64, 0x12,
	0x14, 0x2e, 0x6e, 0x75, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x4e, 0x75,
	0x49, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x6e, 0x75, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75,
	0x62, 0x6d, 0x69, 0x74, 0x4e, 0x75, 0x49, 0x64, 0x52, 0x65, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x47,
	0x65, 0x74, 0x4e, 0x75, 0x49, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x2e, 0x6e, 0x75, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x75, 0x49, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x1a, 0x15, 0x2e, 0x6e, 0x75, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x75, 0x49,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x42, 0x2d, 0x5a, 0x2b, 0x68, 0x61, 0x6c, 0x61,
	0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2d, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6e,
	0x75, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_nu_v1_nu_id_proto_rawDescOnce sync.Once
	file_nu_v1_nu_id_proto_rawDescData = file_nu_v1_nu_id_proto_rawDesc
)

func file_nu_v1_nu_id_proto_rawDescGZIP() []byte {
	file_nu_v1_nu_id_proto_rawDescOnce.Do(func() {
		file_nu_v1_nu_id_proto_rawDescData = protoimpl.X.CompressGZIP(file_nu_v1_nu_id_proto_rawDescData)
	})
	return file_nu_v1_nu_id_proto_rawDescData
}

var file_nu_v1_nu_id_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_nu_v1_nu_id_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_nu_v1_nu_id_proto_goTypes = []interface{}{
	(Gender)(0),                     // 0: nu.v1.Gender
	(NuIdStatus)(0),                 // 1: nu.v1.NuIdStatus
	(*WilayahNU)(nil),               // 2: nu.v1.WilayahNU
	(*GetWilayahNUListReq)(nil),     // 3: nu.v1.GetWilayahNUListReq
	(*GetWilayahNUListResData)(nil), // 4: nu.v1.GetWilayahNUListResData
	(*GetWilayahNUListRes)(nil),     // 5: nu.v1.GetWilayahNUListRes
	(*GetNuIdInfoReq)(nil),          // 6: nu.v1.GetNuIdInfoReq
	(*NuIdInfo)(nil),                // 7: nu.v1.NuIdInfo
	(*GetNuIdInfoRes)(nil),          // 8: nu.v1.GetNuIdInfoRes
	(*SubmitNuIdReq)(nil),           // 9: nu.v1.SubmitNuIdReq
	(*SubmitNuIdRes)(nil),           // 10: nu.v1.SubmitNuIdRes
	(*common.Error)(nil),            // 11: common.Error
}
var file_nu_v1_nu_id_proto_depIdxs = []int32{
	2,  // 0: nu.v1.GetWilayahNUListResData.list:type_name -> nu.v1.WilayahNU
	11, // 1: nu.v1.GetWilayahNUListRes.error:type_name -> common.Error
	4,  // 2: nu.v1.GetWilayahNUListRes.data:type_name -> nu.v1.GetWilayahNUListResData
	1,  // 3: nu.v1.NuIdInfo.status:type_name -> nu.v1.NuIdStatus
	0,  // 4: nu.v1.NuIdInfo.gender:type_name -> nu.v1.Gender
	11, // 5: nu.v1.GetNuIdInfoRes.error:type_name -> common.Error
	7,  // 6: nu.v1.GetNuIdInfoRes.data:type_name -> nu.v1.NuIdInfo
	0,  // 7: nu.v1.SubmitNuIdReq.gender:type_name -> nu.v1.Gender
	11, // 8: nu.v1.SubmitNuIdRes.error:type_name -> common.Error
	3,  // 9: nu.v1.NuIdService.GetWilayahNUList:input_type -> nu.v1.GetWilayahNUListReq
	9,  // 10: nu.v1.NuIdService.SubmitNuId:input_type -> nu.v1.SubmitNuIdReq
	6,  // 11: nu.v1.NuIdService.GetNuIdInfo:input_type -> nu.v1.GetNuIdInfoReq
	5,  // 12: nu.v1.NuIdService.GetWilayahNUList:output_type -> nu.v1.GetWilayahNUListRes
	10, // 13: nu.v1.NuIdService.SubmitNuId:output_type -> nu.v1.SubmitNuIdRes
	8,  // 14: nu.v1.NuIdService.GetNuIdInfo:output_type -> nu.v1.GetNuIdInfoRes
	12, // [12:15] is the sub-list for method output_type
	9,  // [9:12] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_nu_v1_nu_id_proto_init() }
func file_nu_v1_nu_id_proto_init() {
	if File_nu_v1_nu_id_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_nu_v1_nu_id_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WilayahNU); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nu_v1_nu_id_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWilayahNUListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nu_v1_nu_id_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWilayahNUListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nu_v1_nu_id_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWilayahNUListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nu_v1_nu_id_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNuIdInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nu_v1_nu_id_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NuIdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nu_v1_nu_id_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNuIdInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nu_v1_nu_id_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitNuIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nu_v1_nu_id_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitNuIdRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_nu_v1_nu_id_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_nu_v1_nu_id_proto_goTypes,
		DependencyIndexes: file_nu_v1_nu_id_proto_depIdxs,
		EnumInfos:         file_nu_v1_nu_id_proto_enumTypes,
		MessageInfos:      file_nu_v1_nu_id_proto_msgTypes,
	}.Build()
	File_nu_v1_nu_id_proto = out.File
	file_nu_v1_nu_id_proto_rawDesc = nil
	file_nu_v1_nu_id_proto_goTypes = nil
	file_nu_v1_nu_id_proto_depIdxs = nil
}
