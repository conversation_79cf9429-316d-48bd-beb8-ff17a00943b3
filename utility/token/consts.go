package token

import (
	"github.com/gogf/gf/v2/errors/gcode"
)

const (
	HttpToken      = "authorization"
	AcceptLanguage = "accept-language"
)

const (
	JWTSecretKey = "fFD2ji1e3ZVs&*6BNa9#@d"
)

var (
	CodeUserNotFoundError      = gcode.New(15000, "user.not.found", nil)                         // 找不到用户
	CodeOptCodeError           = gcode.New(15001, "account.opt.code.expired.incorrect", nil)     // 验证OPT失败
	CodeVerifyAccessTokenError = gcode.New(15002, "account.access.token.expired.incorrect", nil) // 验证AccessToken失败
	CodeUserLoginBen           = gcode.New(15003, "user.login.ban.time", nil)                    // 用户禁止登录
)
