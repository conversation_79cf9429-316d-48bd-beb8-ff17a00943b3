package token

import (
	"context"
	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/golang-jwt/jwt/v5"
	"strings"
)

type JWTD struct {
	Ty string `json:"ty"`
	jwt.RegisteredClaims
}

// GetAuthorizationHeader 统一获取 Authorization 头，支持 HTTP 和 gRPC
func GetAuthorizationHeader(ctx context.Context) string {
	auth := ""
	// 1. 试着从 HTTP 请求上下文获取 Authorization
	if r := ghttp.RequestFromCtx(ctx); r != nil {
		auth = r.Header.Get(HttpToken)
		if g.IsEmpty(auth) {
			return ""
		}
	} else {
		// 2. 从 gRPC metadata 获取 authorization
		headerMap := grpcx.Ctx.IncomingMap(ctx)
		if !headerMap.Contains(HttpToken) {
			return ""
		}
		auth = headerMap.Get(HttpToken).(string)
		if g.<PERSON>(auth) {
			return ""
		}
	}

	token := strings.TrimPrefix(auth, "Bearer ")
	return strings.TrimSpace(token)
}

// GetUserIdFromToken 从token获取用户id
func GetUserIdFromToken(ctx context.Context, token string) (userId uint64, err error) {
	if g.IsEmpty(token) {
		token = GetAuthorizationHeader(ctx)
		if len(token) == 0 {
			return 0, T(ctx, CodeVerifyAccessTokenError)
		}
	}

	return parseToken(ctx, token)
}

func ParseJWT(token, secretKey string) (*JWTD, error) {
	t, err := jwt.ParseWithClaims(token, &JWTD{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(secretKey), nil
	})
	if err != nil {
		return nil, err
	}
	if claims, ok := t.Claims.(*JWTD); ok && t.Valid {
		return claims, nil
	} else {
		return nil, err
	}
}

// GetUserIdByToken 从 gRPC 上下文中提取 JWT token 并解析出用户 ID。
// NOTICE: 必须在 gRPC controller 中使用，依赖 grpcx.Ctx.IncomingMap(ctx)
func GetUserIdByToken(ctx context.Context) (userId uint64, err error) {
	headerMap := grpcx.Ctx.IncomingMap(ctx)

	// 安全获取 token 字段
	ok := headerMap.Contains(HttpToken)
	if !ok {
		return 0, T(ctx, CodeVerifyAccessTokenError)
	}

	token, ok := headerMap.Get(HttpToken).(string)
	if !ok || len(token) == 0 {
		return 0, T(ctx, CodeVerifyAccessTokenError)
	}

	// 去除 Bearer 前缀
	token = strings.TrimPrefix(token, "Bearer ")
	if len(token) == 0 {
		return 0, T(ctx, CodeVerifyAccessTokenError)
	}

	return parseToken(ctx, token)
}

func parseToken(ctx context.Context, token string) (userId uint64, err error) {

	// 解析 JWT
	jwtd, err := ParseJWT(token, JWTSecretKey)
	if err != nil {
		g.Log().Debug(ctx, "failed to parse JWT", err)
		return 0, T(ctx, CodeVerifyAccessTokenError)
	}

	// 验证类型是否为用户类型
	if jwtd.Ty != "user" {
		g.Log().Debug(ctx, "invalid JWT type", jwtd.Ty)
		return 0, T(ctx, CodeVerifyAccessTokenError)
	}

	// 获取 subject（userId）
	sub, err := jwtd.GetSubject()
	if err != nil || g.IsEmpty(sub) {
		g.Log().Debug(ctx, "invalid JWT subject", jwtd)
		return 0, T(ctx, CodeVerifyAccessTokenError)
	}

	return gconv.Uint64(sub), nil
}

func GetLanguageId(ctx context.Context) (language uint) {
	//'zh-CN': '简体中文',
	//	'en-US': 'English',
	//	'id-ID': 'Bahasa Indonesia',

	headerMap := grpcx.Ctx.IncomingMap(ctx)
	ok := headerMap.Contains(AcceptLanguage)
	if !ok {
		language = 0 // 默认语言
	} else {
		if lang, ok := headerMap.Get(AcceptLanguage).(string); ok && len(lang) > 0 {
			if strings.Contains(lang, "zh-CN") {
				language = 0 // 简体中文
			} else if strings.Contains(lang, "en-US") {
				language = 1 // 英语
			} else if strings.Contains(lang, "id-ID") {
				language = 2 // 印尼语
			} else {
				language = 0 // 默认语言
			}
		} else {
			language = 0 // 默认语言
		}
	}
	return language
}
